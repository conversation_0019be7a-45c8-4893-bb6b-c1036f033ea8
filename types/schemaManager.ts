export interface SchemaAttribute {
    id: string;
    name: string;
    title: string;
    dataType: string;
    description?: string;
    format?: string;
    required?: boolean;
    properties?: SchemaAttribute[]; // For nested attributes when dataType is 'object'
}

export interface SchemaVersion {
    name: string;
    version: number;
    type: string;
    description: string;
    attributes: SchemaAttribute[];
}

export interface SchemaResponse {
    createdAt: string;
    updatedAt: string;
    description: string;
    id: string;
    name: string;
    schemaBody: any;
    schemaUri: string;
    status: string;
    type: string;
    version: number;
    issuedCount: number;
}
