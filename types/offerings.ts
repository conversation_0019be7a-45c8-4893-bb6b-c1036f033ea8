export interface PostOfferingResponse {
    credential_type: string;
    credential_subject: any;
    recipient: string;
    redemption_limit: number;
    expires_at: string;
}

export interface GetOfferingResponse {
    id: string;
    credential_issuer: string;
    credential_configuration_ids: string[];
    display: {
        name: string;
        locale: string;
    };
    grants: {
        authorization_code: {
            issuer_state: string;
        };
    };
    credential_subject: Record<string, unknown>;
    recipient: string | null;
    redemption_limit: number | null;
    expires_at: string | null;
}
