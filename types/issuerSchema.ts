export interface IssuerSchema {
    createdAt: string;
    updatedAt: string;
    description: string;
    status: string;
    id: string;
    name: string;
    type: string;
    version: number;
    schemaUri: string;
    issuedCount: number;
    schemaBody: {
        $metadata: {
            version: string | number;
            type: string;
        };
        title: string;
        properties: {
            credentialSubject: {
                type: string;
                properties: {
                    did: {
                        type: string;
                        title: string;
                    };
                };
                required: string[];
            };
            '@context': {
                type: string[];
            };
            id: {
                type: string;
            };
            issuanceDate: {
                format: string;
                type: string;
            };
            issuer: {
                type: string[];
                format: string;
                properties: {
                    id: {
                        format: string;
                        type: string;
                    };
                };
                required: string[];
            };
            type: {
                type: string[];
                items: {
                    type: string;
                };
            };
            credentialSchema: {
                properties: {
                    id: {
                        format: string;
                        type: string;
                    };
                    type: {
                        type: string;
                    };
                };
                required: string[];
                type: string;
            };
        };
        required: string[];
        type: string;
    };
}

export interface IssuerSchemaBuilder {
    $metadata: {
        version: number;
        type: string;
    };
    title: string;
    description: string;
    $schema: string;
    properties: {
        credentialSubject: {
            description: string;
            title: string;
            properties: Record<string, any>;
            required: string[];
            type: string;
        };
        '@context': {
            type: string[];
        };
        id: {
            type: string;
        };
        issuanceDate: {
            format: string;
            type: string;
        };
        issuer: {
            type: string[];
            format: string;
            properties: {
                id: {
                    format: string;
                    type: string;
                };
            };
            required: string[];
        };
        type: {
            type: string[];
            items: {
                type: string;
            };
        };
        credentialSchema: {
            properties: {
                id: {
                    format: string;
                    type: string;
                };
                type: {
                    type: string;
                };
            };
            required: string[];
            type: string;
        };
    };
    required: string[];
    type: string;
}
