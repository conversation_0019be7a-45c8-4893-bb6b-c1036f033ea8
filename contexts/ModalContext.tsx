'use client';
import { ModalWrapper } from '@/components/ModalWrapper';
import { createContext, ReactNode, useState } from 'react';

interface ModalContextProps {
    setModalContent: (content: ReactNode | null) => void;
    setFullSize: (isFullSize: boolean) => void;
}

export const ModalContext = createContext<ModalContextProps>({
    setModalContent: () => {},
    setFullSize: () => {},
});

export const ModalProvider = ({ children }: { children: ReactNode }) => {
    const [modalContent, setModalContent] = useState<ReactNode | null>(null);
    const [fullSize, setFullSize] = useState<boolean>(false);

    return (
        <ModalContext.Provider value={{ setModalContent, setFullSize }}>
            {children}
            <ModalWrapper fullSize={fullSize}>{modalContent}</ModalWrapper>
        </ModalContext.Provider>
    );
};
