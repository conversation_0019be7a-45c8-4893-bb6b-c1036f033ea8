'use client';
import { ToastWrapper } from '@/components/ToastWrapper';
import { createContext, ReactNode, useState } from 'react';

interface ToastContextProps {
    setToastContent: (content: ReactNode | null) => void;
}

export const ToastContext = createContext<ToastContextProps>({
    setToastContent: () => {},
});

export const ToastProvider = ({ children }: { children: ReactNode }) => {
    const [toastContent, setToastContent] = useState<ReactNode | null>(null);

    return (
        <ToastContext.Provider value={{ setToastContent }}>
            {children}
            <ToastWrapper>{toastContent}</ToastWrapper>
        </ToastContext.Provider>
    );
};
