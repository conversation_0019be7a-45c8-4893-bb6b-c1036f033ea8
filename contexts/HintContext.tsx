'use client';
import { createContext, useContext } from 'react';
import type { HintContent } from '@/data/hints';
import { hintsMap as defaultHintsMap } from '@/data/hints';

export type Placement = 'auto' | 'top' | 'right' | 'bottom' | 'left';

export type HintConfig = {
    hintsMap: Record<string, HintContent>;
    placement?: Placement;
    offset?: number;
    closeOnBlur?: boolean;
    closeOnOutsideClick?: boolean;
};

const HintContext = createContext<HintConfig | null>(null);

export const useHintConfig = () => {
    const ctx = useContext(HintContext);
    if (!ctx) throw new Error('useHintConfig must be used within <HintProvider>');
    return ctx;
};

export function HintProvider({ children, config }: React.PropsWithChildren<{ config?: Partial<HintConfig> }>) {
    const value: HintConfig = {
        hintsMap: config?.hintsMap ?? defaultHintsMap,
        placement: 'auto',
        offset: 8,
        closeOnBlur: true,
        closeOnOutsideClick: true,
        ...config,
    };
    return <HintContext.Provider value={value}>{children}</HintContext.Provider>;
}
