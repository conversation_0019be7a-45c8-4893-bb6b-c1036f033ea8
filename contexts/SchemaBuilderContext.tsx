import { createContext, ReactNode, useContext, useState } from 'react';

export enum SchemaBuilderStepEnum {
    SCHEMA_LIST = 'SCHEMA_LIST',
    SCHEMA_BUILDER = 'SCHEMA_BUILDER',
    SCHEMA_OFFERING = 'SCHEMA_OFFERING',
}

interface SchemaBuilderContextType {
    activeVersionId: string | null;
    step: SchemaBuilderStepEnum;
    setActiveVersionId: (version: string | null) => void;
    setStep: (step: SchemaBuilderStepEnum) => void;
}

export const SchemaBuilderContext = createContext<SchemaBuilderContextType>({
    step: SchemaBuilderStepEnum.SCHEMA_LIST,
    activeVersionId: null,
    setStep: () => {},
    setActiveVersionId: () => {},
});

export const SchemaBuilderProvider = ({ children }: { children: ReactNode }) => {
    const [step, setStep] = useState<SchemaBuilderStepEnum>(SchemaBuilderStepEnum.SCHEMA_LIST);
    const [activeVersionId, setActiveVersionId] = useState<string | null>(null);

    return (
        <SchemaBuilderContext.Provider value={{ step, setStep, activeVersionId, setActiveVersionId }}>
            {children}
        </SchemaBuilderContext.Provider>
    );
};

export const useSchemaBuilderContext = () => {
    const context = useContext(SchemaBuilderContext);
    if (!context) {
        throw new Error('useSchemaBuilderContext must be used within a SchemaBuilderContext');
    }
    return context;
};
