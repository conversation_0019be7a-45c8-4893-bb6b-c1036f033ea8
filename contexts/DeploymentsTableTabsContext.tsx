'use client';
import { createContext, ReactNode, useState } from 'react';
import { DeploymentType } from '@/types/deployments';

type DeploymentsTableTabsContextType = {
    selectedDeploymentType: DeploymentType;
    setSelectedDeploymentType: (type: DeploymentType) => void;
};

const defaultDeploymentsTableTabsContext: DeploymentsTableTabsContextType = {
    selectedDeploymentType: DeploymentType.ISSUER,
    setSelectedDeploymentType: () => {},
};

export const DeploymentsTableTabsContext = createContext<DeploymentsTableTabsContextType>(
    defaultDeploymentsTableTabsContext
);

export const DeploymentsTableTabsProvider = ({ children }: { children: ReactNode }) => {
    const [selectedDeploymentType, setSelectedDeploymentType] = useState<DeploymentType>(DeploymentType.ISSUER);

    return (
        <DeploymentsTableTabsContext.Provider value={{ selectedDeploymentType, setSelectedDeploymentType }}>
            {children}
        </DeploymentsTableTabsContext.Provider>
    );
};
