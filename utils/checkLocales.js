const fs = require('fs');
const path = require('path');

// Funkcja do wczytywania kluczy z pliku en.json
function loadReferenceKeys(enFilePath) {
    const data = JSON.parse(fs.readFileSync(enFilePath, 'utf8'));
    return Object.keys(data);
}

// Funkcja do sprawdzania brakujących i nadmiarowych kluczy względem en.json
function checkKeys(jsonFolder, referenceKeys) {
    const files = fs.readdirSync(jsonFolder).filter(file => file.endsWith('.json'));

    files.forEach(file => {
        const filePath = path.join(jsonFolder, file);
        const data = JSON.parse(fs.readFileSync(filePath, 'utf8'));
        const keys = Object.keys(data);

        const missingKeys = referenceKeys.filter(key => !keys.includes(key));
        const extraKeys = keys.filter(key => !referenceKeys.includes(key));

        console.log(`\x1b[33mPlik ${file}: `);
        if (missingKeys.length > 0) {
            console.log(`\x1b[31m❌ nie zawiera następujących kluczy względem en.json:`);
            console.log(`\x1b[31mBrakujące klucze: \x1b[0m`, missingKeys);
        } else {
            console.log(`\x1b[32m✔️ zawiera wszystkie klucze z en.json.`);
        }

        if (extraKeys.length > 0) {
            console.log(`\x1b[31m❌ zawiera nadmiarowe klucze, których nie ma w en.json:`);
            console.log('\x1b[31mNadmiarowe klucze:', extraKeys);
        } else {
            console.log(`\x1b[32m✔️ nie zawiera nadmiarowych kluczy.`);
        }
        console.log('\n');
    });
}

// Ścieżka do pliku en.json jako wzorzec
const enFilePath = './locales/en.json';

// Ścieżka do folderu z plikami JSON
const jsonFolder = './locales';

// Wczytaj klucze z pliku en.json
const referenceKeys = loadReferenceKeys(enFilePath);

// Sprawdź brakujące i nadmiarowe klucze w plikach JSON względem en.json
checkKeys(jsonFolder, referenceKeys);
