#!/bin/bash

# Ś<PERSON>żka do pliku to_do.json
TODO_FILE="../locales/to_do.json"

# Funkcja rekurencyjna do aktualizacji JSON
update_recursive() {
    local src_data=$1
    local target_data=$2

    # Iteracja po kluczach w źródle
    for subkey in $(echo "$src_data" | jq -r 'keys[]'); do
        src_value=$(echo "$src_data" | jq -c --arg subkey "$subkey" '.[$subkey]')
        
        # Jeś<PERSON> wartość jest obiektem, rekurencja
        if echo "$src_value" | jq -e 'type == "object"' >/dev/null; then
            if echo "$target_data" | jq -e --arg subkey "$subkey" '.[$subkey] | type == "object"' >/dev/null; then
                # Rekurencyjnie aktualizuj istniejący obiekt
                updated_subdata=$(update_recursive "$src_value" "$(echo "$target_data" | jq -c --arg subkey "$subkey" '.[$subkey]')")
                target_data=$(echo "$target_data" | jq --arg subkey "$subkey" --argjson value "$updated_subdata" '.[$subkey] = $value')
            else
                # Dodaj nowy obiekt
                target_data=$(echo "$target_data" | jq --arg subkey "$subkey" --argjson value "$src_value" '.[$subkey] = $value')
            fi
        else
            # Zaktualizuj lub dodaj wartość
            target_data=$(echo "$target_data" | jq --arg subkey "$subkey" --argjson value "$src_value" '.[$subkey] = $value')
        fi
    done

    echo "$target_data"
}

# Przetwarzanie języków z pliku TODO_FILE
jq -r 'keys[]' "$TODO_FILE" | while read -r lang; do
    TARGET_FILE="../locales/${lang}.json"

    if [ -f "$TARGET_FILE" ]; then
        echo "Aktualizowanie tłumaczeń dla języka: $lang"
        src_translations=$(jq -c --arg lang "$lang" '.[$lang]' "$TODO_FILE")
        target_translations=$(jq -c '.' "$TARGET_FILE")
        
        updated_translations=$(update_recursive "$src_translations" "$target_translations")
        echo "$updated_translations" | jq '.' > "$TARGET_FILE"
    else
        echo "Plik dla języka $lang nie istnieje. Tworzenie nowego pliku: $TARGET_FILE"
        jq -c --arg lang "$lang" '.[$lang]' "$TODO_FILE" | jq '.' > "$TARGET_FILE"
    fi
done

echo "Zakończono aktualizację tłumaczeń."
