apiVersion: apps/v1
kind: Deployment
metadata:
    name: one-click-deployer-front-stg
    namespace: customer-issuer-verifier
spec:
    replicas: 1
    selector:
        matchLabels:
            app: one-click-deployer-front-stg
    template:
        metadata:
            labels:
                app: one-click-deployer-front-stg
        spec:
            containers:
                - name: one-click-deployer-front-stg
                  image: 309596z9.c1.gra9.container-registry.ovh.net/empe/services/one-click-deployer-front:0.1.1-beta1
                  imagePullPolicy: Always
                  ports:
                      - containerPort: 3000
                  env:
                      - name: STRIPE_PUBLISHABLE_KEY
                        value: 'pk_test_51RiysY2XZA2cA55kRnPFx8KUMtwZcDA1zkP00OjWU7rX5BB8emBVQ11lSNDGZ7LRrws7cgKuMfCxS5J6FNMslMT500Lb2Zv4XZ'
                      - name: STRIPE_PRICING_TABLE_ID
                        value: 'prctbl_1RjeSx2XZA2cA55k5DvZmIVd'
                      - name: API_URL
                        value: 'https://deploy-stg.evdi.app/api'
                      - name: DEPLOYMENT_TYPE
                        value: 'EMPE_OVH_K8S_DEPLOYMENT'
            imagePullSecrets:
                - name: harbor-registry-secret
            volumes:
                - name: kubeconfig-volume
                  secret:
                      secretName: kubeconfig-secret
