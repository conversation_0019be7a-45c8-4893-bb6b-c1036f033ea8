export const markdownContent = `

# Terms & Conditions

**Empeiria Product Terms and Conditions**

**Effective Date**: July 24, 2025

**Legal Entity**: Empeiria LTD, 15th, Al Sarab Tower, ADGM Square, Al Maryah Island, Abu Dhabi, United Arab Emirates

### 1. Introduction

These Terms and Conditions (“Terms” or “Agreement”) govern your access to and use of all websites, software, APIs, applications, content, programs, and products (collectively, the “Services”) provided by Empeiria LTD (“Empeiria,” “we,” “us,” or “our”). The Services include a one-click deployment portal for deploying issuer services (enabling creation and management of verifiable credentials, schemas, and decentralized identities) and verifier services (facilitating credential verification), powered by \`empe\` tokens on our proprietary blockchain, provided as part of your subscription. By accessing or using the Services, you (“User,” “you,” or “your”) agree to be bound by these Terms. If you do not agree, do not use the Services.

Your use is also governed by our [Privacy Policy](/signup/policy), incorporated herein by reference.

### 2. Description of Services

Empeiria provides an API-first platform and software tools for issuing, verifying, and managing W3C-compliant verifiable credentials and decentralized identities using our proprietary \`did:empe\` method. The Services allow developers, businesses, and organizations to deploy issuer and verifier services, create and manage schemas, revoke credentials, and publish data (e.g., revocations, schemas, timestamps) to our \`empe\` blockchain. Tokens required for blockchain operations are included in your subscription, subject to usage limits. You may use the Services for personal, internal business, or commercial purposes in accordance with these Terms and applicable laws.

### 3. Eligibility and Account Registration

- You must be at least 18 years old or have legal capacity to enter this Agreement.
- To access certain features, you must register an account (“Account”) with accurate, current, and complete information, including a valid credit card for trial or paid plans to prevent spam.
- You agree to maintain and update Account information and keep login credentials (e.g., API keys) confidential.
- You are responsible for all activities under your Account and must notify Empeiria immediately of unauthorized use or security breaches at [<EMAIL>](mailto:<EMAIL>).
- Empeiria may verify Accounts for compliance with anti-fraud or KYC requirements.

### 4. Subscription Plans and Usage Limits

- **Plans**: Services are offered under tiered plans (Sandbox, Basic, Pro, Enterprise), each with specific features and limits, as detailed on our pricing page [https://empe.io/pricing](https://empe.io/pricing) or in your order form. Limits include, but are not limited to:
    - Monthly credential issuances.
    - Total active schema types (non-resetting).
    - Monthly blockchain publications (e.g., for revocations, schemas, timestamps).
    - Verifier operations.
- **Sandbox Plan**: The Sandbox plan is a trial offering (1-month duration, subject to change) limited to our testnet, unsuitable for mainnet production use. A credit card is required at sign-up, and a nominal fee (\$10/month, subject to change) applies post-trial to continue access. You may cancel or upgrade to Basic/Pro at any time.
- **Upgrades**: Upon upgrading plans (e.g., from Basic to Pro), a new billing cycle begins, with fees prorated based on unused days in the prior plan (e.g., upgrading mid-month from Basic (\$100/month) to Pro (\$500/month) incurs a \$450 charge for the first Pro month). The prior plan is canceled, and limits reset.
- **Add-ons**: You may purchase one-time add-ons (e.g., additional credential issuances) to supplement plan limits. Add-ons do not reset monthly, and terms (e.g., expiration) are specified in the add-on description.
- **Exceeding Limits**: Exceeding plan limits prevents further operations (e.g., issuing credentials) until the next billing cycle or add-on purchase. Empeiria may, in the future, offer automatic add-on purchases for overages, subject to notice.
- **Enterprise**: Enterprise plans may include custom limits, support, or terms via separate agreements that supersede these Terms, as agreed per case.

### 5. License and Use Restrictions

- Subject to compliance with these Terms, Empeiria grants you a limited, non-exclusive, non-transferable, revocable license to access and use the Services per your plan.
- You may not:
    - Reverse engineer, decompile, or derive source code from the Services.
    - Modify, copy, sell, resell, or create derivative works of the Services.
    - Use the Services for unlawful, fraudulent, or harmful purposes.
    - Circumvent security or access controls.
    - Exceed API rate limits, usage quotas, or blockchain publication limits as defined in your plan.
    - Use automated systems to scrape data without written consent.
    - Publish credentials, schemas, or revocations in violation of W3C standards or applicable laws.

### 6. Tokens for Blockchain Operations

- Subscriptions include \`empe\` tokens for blockchain operations (e.g., publishing revocations, schemas, timestamps) on our proprietary \`empe\` blockchain. These tokens are for internal platform use only, non-transferable, non-tradable, and cannot be used outside your plan’s limits or to pay for other services.
- Usage is subject to plan limits. Lower-tier plans may require batching blockchain publications (e.g., for revocations), which may delay public visibility of changes until published. You are responsible for scheduling publications to ensure timely updates (e.g., credential revocations).

### 7. User Content and Intellectual Property

- You retain ownership of all data, credentials, schemas, or materials you submit or create through the Services (“Your Content”), consistent with SSI principles.
- You grant Empeiria a worldwide, royalty-free, sublicensable license to use, reproduce, modify, adapt, and distribute Your Content solely to provide and improve the Services.
- You warrant that Your Content does not infringe third-party rights and that you have necessary consents.
- All intellectual property in the Services (e.g., software, \`did:empe\` method, \`empe\` blockchain, trademarks) is owned by Empeiria or its licensors. You may not use our trademarks without written permission.

### 8. Fees and Payment

- Access to certain Services requires payment of fees as per your plan or order form, payable in advance via Stripe in USD (local currency conversion may apply per Stripe’s terms).
- Fees are non-refundable except as required by law. No refunds are offered for trial or Sandbox use to prevent abuse.
- Exceeding limits blocks operations without overage fees, though add-ons may be purchased.
- You are responsible for applicable taxes.
- Empeiria may change pricing with 30 days’ notice via our website or email to [<EMAIL>](mailto:<EMAIL>).
- Failure to pay may result in suspension or termination.

### 9. Service Availability and Support

- Empeiria aims for high availability, targeting ~99% uptime (excluding scheduled maintenance) for non-enterprise plans, but provides no formal Service Level Agreement (SLA) unless specified in an Enterprise agreement.
- **Support**:
    - Sandbox: Access to community support (e.g., Discord server).
    - Basic: Email support ([<EMAIL>](mailto:<EMAIL>)).
    - Pro: Dedicated chat support and a Technical Account Manager (TAM).
    - Enterprise: Custom support per agreement.
- Scheduled maintenance will be announced via email or dashboard with reasonable notice (e.g., 24 hours).

### 10. Privacy and Data Protection

- Your use is subject to our [Privacy Policy](/signup/policy), which details data collection, use, and protection.
- Empeiria supports W3C Verifiable Credentials and minimizes data collection per SSI principles. You must comply with applicable data protection laws (e.g., UAE PDPL, GDPR) when processing personal data.
- You may download backups of Your Content at any time. Post-termination, data is retained for 30 days for backup purposes, then deleted, unless otherwise agreed.

### 11. Third-Party Services

- The Services may integrate with third-party blockchain networks, wallets, or tools. Empeiria is not responsible for their availability, content, or practices. Your use of third-party services is governed by their terms.

### 12. User Conduct and Prohibited Activities

You may not use the Services to:

- Violate laws, regulations, or W3C standards.
- Issue fraudulent or deceptive credentials.
- Infringe intellectual property or privacy rights.
- Engage in illegal or unethical activities, including but not limited to gambling, adult content, illicit financial services, or any activities prohibited under UAE law.
- Transmit harmful or offensive content.
- Disrupt the Services or attempt unauthorized access.
- Send spam or impersonate others.

### 13. Disclaimers and Limitation of Liability

- **No Warranty**: The Services are provided “as is” and “as available” without warranties, express or implied, including for merchantability, fitness, or non-infringement. Empeiria does not guarantee uninterrupted, secure, or error-free operation, or that credentials/revocations will be immediately effective if batched.
- **No Third-Party Liability**: Empeiria is not liable for third-party actions, content, or data.
- **Limitation of Liability**: To the maximum extent permitted by law, Empeiria’s aggregate liability shall not exceed the greater of (a) fees paid by you in the prior 12 months, or (b) \$100. Empeiria is not liable for indirect, incidental, or consequential damages, including loss of profits, data, or goodwill.
- **Exclusions**: Some jurisdictions may limit warranty or liability exclusions; our liability is limited to the fullest extent permitted.
- **Force Majeure**: Empeiria is not liable for delays due to causes beyond our control, including \`empe\` blockchain network failures, acts of God, or infrastructure issues.

### 14. Indemnification

You agree to indemnify, defend, and hold harmless Empeiria, its affiliates, and agents from claims or damages arising from your use, violation of these Terms, or infringement of third-party rights, including issues from credentials you issue or verify.

### 15. Termination

- You may cancel your plan at any time. Empeiria may terminate with written notice.
- Empeiria may suspend or terminate your access for violations of these Terms or laws.
- Upon termination, your access ceases, deployed services are deactivated, and data is retained for 30 days before deletion, unless otherwise agreed. You are responsible for exporting Your Content prior to termination.

### 16. Governing Law and Dispute Resolution

- These Terms are governed by the laws of the United Arab Emirates.
- Disputes shall be subject to the exclusive jurisdiction of Abu Dhabi courts, with mediation attempted first.

### 17. Changes to Terms

- Empeiria may update these Terms by posting revisions on our website or notifying you at [<EMAIL>](mailto:<EMAIL>).
- Continued use after changes constitutes acceptance.

### 18. Confidentiality

- Both parties agree to protect confidential information (e.g., API keys, deployment configs, \`empe\` tokens) and use it only as necessary to provide or use the Services.

### 19. Export Controls and Restricted Industries

- You must comply with applicable export control laws and may not use the Services in sanctioned countries or for prohibited industries, including but not limited to gambling, adult content, illicit financial services, or any activities deemed unethical or illegal under UAE law.

### 20. Entire Agreement

- These Terms, along with any order forms, pricing page details, or Enterprise agreements, constitute the entire agreement between you and Empeiria, superseding prior agreements. Enterprise plans may be subject to separate terms, as agreed.
- If any provision is invalid, the remaining provisions remain in effect.

### 21. Contact Information

For questions, contact:

- Email: [<EMAIL>](mailto:<EMAIL>) (legal inquiries) or [<EMAIL>](mailto:<EMAIL>) (support)  
- Address: Empeiria LTD, 15th, Al Sarab Tower, ADGM Square, Al Maryah Island, Abu Dhabi, UAE

`;
