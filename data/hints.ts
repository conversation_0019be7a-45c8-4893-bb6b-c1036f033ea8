export type HintContent = { title?: string; description: string };

// Global hints map. Extend with more IDs as needed.
export const hintsMap: Record<string, HintContent> = {
    issuer_details_schema_builder_attributes: {
        title: 'Attributes',
        description:
            'The list is an editable view of the attributes of the schema being built. Here you can easily add, remove, and edit the attributes of your schema.',
    },
    issuer_details_schema_builder_json: {
        title: 'Json Editor',
        description:
            'The JSON editor lets you edit the schema in JSON format. You can paste an existing schema, edit it manually, or use the editor for automatic validation and formatting.',
    },
    issuer_details_schema_builder_ai: {
        title: 'AI',
        description:
            "Your AI assistant can help you create the schema. Enter a prompt describing the schema you want to build. We'll help you create the schema with no hassle!",
    },
    issuer_details_schema_builder_history: {
        title: 'History',
        description:
            'In this view you have access to all saved versions of the schema. You can compare versions, restore a previous version, or see the change history.',
    },
    issuer_details_schema_schema_metadata: {
        title: 'Schema Metadata',
        description:
            "Here you can edit the schema's metadata: information that describes the schema, such as its name and description.",
    },
    issuer_details_schema_list_define_schema: {
        title: 'Define Schema',
        description:
            'Here you can create a new schema. Enter its name, description, and type, then click "Define New Schema" to add the new schema.',
    },
    issuer_details_schema_list_list: {
        title: 'Schema List',
        description: "In this view you have access to all of your issuer's schemas and all versions of those schemas.",
    },
    issuer_details_schema_list_json_preview: {
        title: 'Schema Preview',
        description: "Here you have a quick preview of your schema. If it's a draft, you can go to editing.",
    },
};
