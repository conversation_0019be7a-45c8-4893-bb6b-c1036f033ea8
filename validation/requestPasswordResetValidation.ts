import { z } from 'zod';
import { validateEmail } from './common';
import { FormFieldsTypes } from '@/common/formFieldsTypes';

/**
 * Creates the Zod schema for the request password reset form.
 */
export const createRequestPasswordResetSchema = (t: (key: string) => string) =>
    z.object({
        email: validateEmail(t),
    });

/**
 * Infers the TypeScript type from the Zod schema.
 */
export type RequestPasswordResetFormData = z.infer<ReturnType<typeof createRequestPasswordResetSchema>>;

/**
 * Defines the input types for the form fields, used for rendering.
 */
export const RequestPasswordResetFormInputsTypes: Record<keyof RequestPasswordResetFormData, FormFieldsTypes> = {
    email: FormFieldsTypes.INPUT,
};
