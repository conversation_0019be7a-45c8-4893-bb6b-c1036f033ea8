import { z } from 'zod';
import {
    validateBlockchainConfig,
    validateDeploymentName,
    validateNameWithUppercase,
    validateReadableName,
    validateVersionId,
} from './common';
import { FormFieldsTypes } from '@/common/formFieldsTypes';

export const createNewIssuerSchema = (t: (key: string) => string) =>
    z.object({
        issuerReadableName: validateReadableName(t),
        issuerName: validateDeploymentName(t),
        organizationName: validateNameWithUppercase(t),
        versionId: validateVersionId(t),
        blockchainConfigurationId: validateBlockchainConfig(t),
    });

export type NewIssuerForm = z.infer<ReturnType<typeof createNewIssuerSchema>>;

export const NewIssuerFormInputsTypes: Record<keyof NewIssuerForm, FormFieldsTypes> = {
    issuerName: FormFieldsTypes.INPUT_WITH_POSTFIX,
    issuerReadableName: FormFieldsTypes.INPUT,
    organizationName: FormFieldsTypes.INPUT,
    versionId: FormFieldsTypes.SELECT,
    blockchainConfigurationId: FormFieldsTypes.SELECT,
};
