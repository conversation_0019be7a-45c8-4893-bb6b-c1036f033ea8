import { FormFieldsTypes } from '@/common/formFieldsTypes';
import { z } from 'zod';
import { validateVersion } from './common';

export const createUpgradeIssuerVersionSchema = (t: (key: string) => string) =>
    z.object({
        version: validateVersion(t),
    });

export type UpgradeIssuerVersionForm = z.infer<ReturnType<typeof createUpgradeIssuerVersionSchema>>;

export const UpgradeIssuerVersionFormInputsTypes: Record<keyof UpgradeIssuerVersionForm, FormFieldsTypes> = {
    version: FormFieldsTypes.SELECT,
};
