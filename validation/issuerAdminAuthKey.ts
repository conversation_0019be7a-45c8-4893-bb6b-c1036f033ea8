import { FormFieldsTypes } from '@/common/formFieldsTypes';
import { z } from 'zod';
import { validateRequiredField } from './common';

export const createSchemaValidation = (t: (key: string) => string) => {
    return z.object({
        authorization: validateRequiredField(t),
    });
};
export type SchemaFormData = z.infer<ReturnType<typeof createSchemaValidation>>;

export const SchemaAuthFormDataInputsTypes: Record<keyof SchemaFormData, FormFieldsTypes> = {
    authorization: FormFieldsTypes.INPUT,
};
