import { z } from 'zod';
import { validateRequired<PERSON>ield } from './common';
import { FormFieldsTypes } from '@/common/formFieldsTypes';

export const defineSchemaValidation = (t: (key: string) => string) => {
    return z.object({
        name: validate<PERSON><PERSON>quired<PERSON><PERSON>(t),
        description: validateRequired<PERSON><PERSON>(t),
        type: validateRequired<PERSON>ield(t),
    });
};

export type DefineSchemaFormData = z.infer<ReturnType<typeof defineSchemaValidation>>;

export const DefineSchemaFormInputsTypes: Record<keyof DefineSchemaFormData, FormFieldsTypes> = {
    name: FormFieldsTypes.INPUT,
    description: FormFieldsTypes.INPUT,
    type: FormFieldsTypes.INPUT,
};
