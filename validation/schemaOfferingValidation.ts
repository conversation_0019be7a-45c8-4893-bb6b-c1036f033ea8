import { z } from 'zod';
import { FormFieldsTypes } from '@/common/formFieldsTypes';

// Helper function to create validation for different field types
const createFieldValidation = (fieldSchema: any, t: (key: string) => string) => {
    const { type, format, title } = fieldSchema;

    let validation: any;

    // <PERSON>le required fields - will be applied later based on schema.required array
    if (type === 'string') {
        if (format === 'date-time') {
            validation = z.string().min(1, t('validation_messages.datetime_required'));
        } else if (format === 'email') {
            validation = z.string().email(t('validation_messages.email_invalid_format'));
        } else if (format === 'uri') {
            validation = z.string().url(t('validation_messages.uri_invalid_format'));
        } else {
            validation = z.string();
        }
    } else if (type === 'number') {
        validation = z.coerce.number();
    } else if (type === 'boolean') {
        validation = z.boolean();
    } else if (type === 'object') {
        // For nested objects, we'll handle them recursively
        validation = z.record(z.any());
    }

    return validation;
};

// Helper function to determine FormFieldsTypes based on schema
const getFormFieldType = (fieldSchema: any): FormFieldsTypes => {
    const { type, format, enum: enumValues } = fieldSchema;

    if (enumValues && enumValues.length > 0) {
        return FormFieldsTypes.SELECT;
    }

    if (type === 'string') {
        if (format === 'date-time') {
            return FormFieldsTypes.DATETIME;
        } else if (format === 'email') {
            return FormFieldsTypes.INPUT;
        } else if (format === 'uri') {
            return FormFieldsTypes.INPUT;
        } else {
            return FormFieldsTypes.INPUT;
        }
    } else if (type === 'number') {
        return FormFieldsTypes.INPUT;
    } else if (type === 'boolean') {
        return FormFieldsTypes.CHECKBOX;
    }

    return FormFieldsTypes.INPUT;
};

// Main function to create dynamic schema validation
export const createSchemaOfferingValidation = (credentialSubjectSchema: any, t: (key: string) => string) => {
    const { properties, required = [] } = credentialSubjectSchema;

    if (!properties || typeof properties !== 'object') {
        throw new Error('Invalid credentialSubject schema: missing properties');
    }

    const schemaObject: Record<string, any> = {};

    // Create validation for each property
    Object.entries(properties).forEach(([fieldName, fieldSchema]: [string, any]) => {
        let fieldValidation = createFieldValidation(fieldSchema, t);

        // Apply required validation if field is in required array
        if (required.includes(fieldName)) {
            if (fieldSchema.type === 'string') {
                fieldValidation = fieldValidation.min(1, t('validation_messages.required_field'));
            } else if (fieldSchema.type === 'boolean') {
                fieldValidation = fieldValidation.refine((val: any) => val === true, {
                    message: t('validation_messages.checkbox_must_be_checked'),
                });
            }
        } else {
            // Make optional fields truly optional
            fieldValidation = fieldValidation.optional();
        }

        schemaObject[fieldName] = fieldValidation;
    });

    return z.object(schemaObject);
};

// Helper function to create FormFieldsTypes mapping
export const createSchemaOfferingFormTypes = (credentialSubjectSchema: any): Record<string, FormFieldsTypes> => {
    const { properties } = credentialSubjectSchema;

    if (!properties || typeof properties !== 'object') {
        return {};
    }

    const formTypes: Record<string, FormFieldsTypes> = {};

    Object.entries(properties).forEach(([fieldName, fieldSchema]: [string, any]) => {
        formTypes[fieldName] = getFormFieldType(fieldSchema);
    });

    return formTypes;
};

// Helper function to create select options from enum values
export const createSelectOptionsFromSchema = (credentialSubjectSchema: any) => {
    const { properties } = credentialSubjectSchema;

    if (!properties || typeof properties !== 'object') {
        return {};
    }

    const selectOptions: Record<string, Array<{ value: string; label: string }>> = {};

    Object.entries(properties).forEach(([fieldName, fieldSchema]: [string, any]) => {
        if (fieldSchema.enum && Array.isArray(fieldSchema.enum)) {
            selectOptions[fieldName] = fieldSchema.enum.map((value: string) => ({
                value,
                label: value,
            }));
        }
    });

    return selectOptions;
};

// Type for the dynamic form data
export type SchemaOfferingFormData = Record<string, any>;
