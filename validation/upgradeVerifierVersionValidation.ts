import { FormFieldsTypes } from '@/common/formFieldsTypes';
import { z } from 'zod';
import { validateVersion } from './common';

export const createUpgradeVerifierVersionSchema = (t: (key: string) => string) =>
    z.object({
        version: validateVersion(t),
    });

export type UpgradeVerifierVersionForm = z.infer<ReturnType<typeof createUpgradeVerifierVersionSchema>>;

export const UpgradeVerifierVersionFormInputsTypes: Record<keyof UpgradeVerifierVersionForm, FormFieldsTypes> = {
    version: FormFieldsTypes.SELECT,
};
