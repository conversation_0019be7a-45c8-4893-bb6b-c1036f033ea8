import { FormFieldsTypes } from '@/common/formFieldsTypes';
import { z } from 'zod';
import { validatePassword, validateRepeatPassword } from '@/validation/common';

export const createPasswordResetSchema = (t: (key: string) => string) =>
    z.object({
        password: validatePassword(t),
        repeatPassword: validateRepeatPassword(t),
    });

export type PasswordResetForm = z.infer<ReturnType<typeof createPasswordResetSchema>>;

export const PasswordResetFormInputsTypes: Record<keyof PasswordResetForm, FormFieldsTypes> = {
    password: FormFieldsTypes.PASSWORD,
    repeatPassword: FormFieldsTypes.PASSWORD,
};
