import { FormFieldsTypes } from '@/common/formFieldsTypes';
import { z } from 'zod';

export const createConfirmEmailSchema = (t: (key: string) => string) =>
    z.object({
        token: z.string().min(6, { message: t('validation_messages.token_required') }),
    });

export type ConfirmEmailForm = z.infer<ReturnType<typeof createConfirmEmailSchema>>;

export const ConfirmEmailFormInputsTypes: Record<keyof ConfirmEmailForm, FormFieldsTypes> = {
    token: FormFieldsTypes.PIN_CODE,
};
