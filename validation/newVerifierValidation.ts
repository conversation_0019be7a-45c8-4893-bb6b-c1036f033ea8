import { z } from 'zod';
import { validateBlockchainConfig, validateDeploymentName, validateReadableName, validateVersionId } from './common';
import { FormFieldsTypes } from '@/common/formFieldsTypes';

export const createNewVerifierSchema = (t: (key: string) => string) =>
    z.object({
        verifierReadableName: validateReadableName(t),
        verifierName: validateDeploymentName(t),
        versionId: validateVersionId(t),
        blockchainConfigurationId: validateBlockchainConfig(t),
    });

export type NewVerifierForm = z.infer<ReturnType<typeof createNewVerifierSchema>>;

export const NewVerifierFormInputsTypes: Record<keyof NewVerifierForm, FormFieldsTypes> = {
    verifierName: FormFieldsTypes.INPUT_WITH_POSTFIX,
    verifierReadableName: FormFieldsTypes.INPUT,
    versionId: FormFieldsTypes.SELECT,
    blockchainConfigurationId: FormFieldsTypes.SELECT,
};
