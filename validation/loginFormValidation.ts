import { z } from 'zod';
import { validateEmail, validatePassword } from './common';
import { FormFieldsTypes } from '@/common/formFieldsTypes';

export const createLoginSchema = (t: (key: string) => string) =>
    z.object({
        email: validateEmail(t),
        password: validatePassword(t),
    });

export type LoginFormData = z.infer<ReturnType<typeof createLoginSchema>>;

export const LoginFormDataInputsTypes: Record<keyof LoginFormData, FormFieldsTypes> = {
    email: FormFieldsTypes.INPUT,
    password: FormFieldsTypes.PASSWORD,
};
