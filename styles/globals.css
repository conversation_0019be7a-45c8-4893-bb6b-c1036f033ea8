@tailwind base;
@tailwind components;
@tailwind utilities;

html,
body {
    height: 100%;
}

.without-scrollbar {
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.bg-image-logo {
    position: fixed;
    background-image: url('/img/empe-logo-bg.png');
    background-size: 20%;
    background-position: center;
    background-repeat: no-repeat;
}

.markdown h1 {
    @apply text-xl mb-6 flex justify-center items-center;
}
.markdown h2 {
    @apply text-lg font-semibold mb-3;
}
.markdown p {
    @apply text-sm leading-7 mb-4;
}
.markdown ul {
    @apply list-disc list-inside mb-4;
}
.markdown li {
    @apply text-sm leading-7;
}
.markdown a {
    @apply text-blue-400 hover:underline py-10 font-semibold;
}

/* for WebKit browsers (Chrome, Safari, Edge, etc) */
input[type='datetime-local']::-webkit-calendar-picker-indicator {
    filter: invert(1) brightness(2);
}

/* for Firefox */
input[type='datetime-local']::-moz-calendar-picker-indicator {
    filter: invert(1) brightness(2);
}

/* Blokowanie zmian stylów przez menedżery haseł */
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
    -webkit-box-shadow: inset 0 0 0px 1000px transparent !important;
    -webkit-text-fill-color: #00afff;
    background-color: transparent !important;
    background-image: none !important;
    transition: background-color 5000s ease-in-out 0s !important;
}

/* Dla pól password specjalnie */
input[type='password']:-webkit-autofill,
input[type='password']:-webkit-autofill:hover,
input[type='password']:-webkit-autofill:focus,
input[type='password']:-webkit-autofill:active {
    -webkit-box-shadow: inset 0 0 0px 1000px transparent !important;
    -webkit-text-fill-color: #00afff;
    background-color: transparent !important;
    background-image: none !important;
    transition: background-color 5000s ease-in-out 0s !important;
}
