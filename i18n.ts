import { getRequestConfig } from 'next-intl/server';
import { getLocale } from './services/localesService';
import { SUPPORTED_LOCALES } from './common/supportedLocales';

export default getRequestConfig(async () => {
    const locale = await getLocale();

    const finalLocale = Object.keys(SUPPORTED_LOCALES).includes(locale) ? locale : SUPPORTED_LOCALES.en;

    return {
        locale: finalLocale,
        messages: (await import(`./locales/${finalLocale}.json`)).default,
    };
});
