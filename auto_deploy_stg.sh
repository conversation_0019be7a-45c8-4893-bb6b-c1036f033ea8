#!/bin/bash

export KUBECONFIG=~/.kube/config_mainnet
# Define variables
DEPLOYMENT_CATALOG="./deployment-stg"
FRONTEND_CATALOG="./"
DEPLOYMENT_FILE="$DEPLOYMENT_CATALOG/one-click-deployer-front-deployment.yaml"
IMAGE_PREFIX="309596z9.c1.gra9.container-registry.ovh.net/empe/services/one-click-deployer-front:"

# Debugging step: Show the current image line in the deployment file
echo "Current image line in deployment file:"
grep "image:" "$DEPLOYMENT_FILE"

# Use grep to extract the current image tag
IMAGE_TAG=$(grep -oE '309596z9\.c1\.gra9\.container-registry\.ovh\.net/empe/services/one-click-deployer-front:[0-9]+\.[0-9]+\.[0-9]+' "$DEPLOYMENT_FILE" | grep -oE '[0-9]+\.[0-9]+\.[0-9]+')

if [[ -z "$IMAGE_TAG" ]]; then
  echo "Error: Unable to extract the current image tag from the deployment file."
  exit 1
fi

# Bump the version (increment the patch version)
IFS='.' read -r major minor patch <<< "$IMAGE_TAG"
new_patch=$((patch + 1))
NEW_IMAGE_TAG="$major.$minor.$new_patch"
NEW_IMAGE="$IMAGE_PREFIX$NEW_IMAGE_TAG"

echo "New image name: $NEW_IMAGE"

# Update version in package.json
sed -i '' "s/\"version\": \".*\"/\"version\": \"$NEW_IMAGE_TAG\"/" package.json

# Update the image tag in the deployment file
sed -i '' "s|image: .*|image: $NEW_IMAGE|g" "$DEPLOYMENT_FILE"

# Commit changes
git add package.json "$DEPLOYMENT_FILE"
git commit -m "chore: bump version to $NEW_IMAGE_TAG"
git tag -a "v$NEW_IMAGE_TAG" -m "Version $NEW_IMAGE_TAG"

# Build the new Docker image
docker build --platform linux/amd64 -t "$NEW_IMAGE" "$FRONTEND_CATALOG"

# Push the new Docker image to the registry
docker push "$NEW_IMAGE"

# Apply the updated deployment
echo $DEPLOYMENT_CATALOG
kubectl apply -f "$DEPLOYMENT_CATALOG/" --validate=false