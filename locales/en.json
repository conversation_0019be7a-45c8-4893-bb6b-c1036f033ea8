{"start_screen": {"title": "Welcome to One-Click Deployment", "subtitle": "Choose what you want to do", "login": "Log in", "signup": "Sign Up"}, "confirm_email": {"title": "Email Confirmation", "button": "Confirm Email", "token_label": "Confirmation code"}, "request_password_reset": {"title": "Reset Password", "description": "Please enter your email to receive a password reset link.", "button": "Send e-mail request", "email_label": "E-mail", "email_placeholder": "Type your email here", "success_label": "Reset Password", "success_title": "Check your mailbox", "success_description_part1": "The password reset link was sent to:"}, "change_password": {"label": "Change Password", "success_title": "Check your mailbox", "success_description": "Please check your email to reset your password.", "error_title": "Error sending reset link"}, "reset_password": {"title": "Reset Password", "button": "Reset Password", "password_label": "New Password", "repeat_password_label": "Repeat New Password"}, "navbar_dashboard": {"request_password_reset": "Change password"}, "login_screen": {"title": "Sign in", "button": "Log in", "placeholder": "Type here", "email_label": "E-mail", "password_label": "Password", "request_password_reset": "Forgot Password?", "not_have": "Don't have an account?", "sign_up": "Sign up"}, "signup_screen": {"title": "Sign Up", "button": "Sign Up", "placeholder": "Type here", "email_label": "E-mail", "password_label": "Password", "first_name_label": "First name", "last_name_label": "Last name", "organization_name_label": "Organization name", "repeat_password_label": "Repeat password", "credit_code_text_label": "Access code", "account_question": "Do you have an account?", "account_question_link": "Log in", "terms_and_conditions_label": "Terms & Conditions", "policy_label": "Privacy Policy", "phone_number_label": "Phone number"}, "deployments_table": {"widget_title": "My Deployments", "empty_info_title": "You don't have any deployments yet.", "empty_info_description": "You don't have any deployment yet", "name": "Name", "type": "Type", "status": "Status", "details_button": "Details"}, "details_of_deployment": {"get_logs_button": "Get Logs", "issuer_title": "Details of Issuer", "verifier_title": "Details of Verifier", "edit_button": "Edit", "new_secret_button": "Upgrade secret", "upgrade_version_button": "Upgrade version", "show_server_logs_button": "Show Server Logs", "instruction": "Instruction", "danger_zone": {"title": "Danger Zone", "subtitle": "Irreversible and destructive actions", "delete_deployment": {"title": "Delete this deployment", "description": "Once you delete a deployment, there is no going back. Please be certain.", "button": "Delete"}, "upgrade_secret": {"title": "Upgrade deployment secret", "description": "This will regenerate the deployment secret and may cause service interruption.", "button": "Upgrade Secret"}, "upgrade_version": {"title": "Upgrade deployment version", "description": "This will regenerate the deployment version and may cause service interruption.", "button": "Upgrade Version"}}}, "validation_messages": {"email_schema": "Invalid email address", "password_schema": "Password must be at least 8 characters long", "email_required": "E-mail is required", "email_message": "Type a valid email address", "password_required": "Password is required", "password_message": "Password must be at least 8 characters long", "repeat_password_required": "Repeat password is required", "organization_name_required": "Organization name is required", "password_length": "Password must be at least 8 characters long", "email_invalid": "Type a valid email address", "passwords_must_match": "Passwords must match", "name_required": "Name is required", "name_format": "Format of name is invalid", "credit_code": "Credit code is required", "version_required": "Version is required", "version_format": "Version format is invalid", "checkbox_required": "You must accept the terms and conditions", "blockchain_config_required": "Blockchain configuration is required", "token_required": "Confirmation code is required", "deployment_name_min_length": "Deployment name must be at least 3 characters long", "deployment_name_start_lowercase": "Deployment name must start with a lowercase letter", "deployment_name_end_alphanumeric": "Deployment name must end with a lowercase letter or number", "deployment_name_invalid_chars": "Deployment name can only contain lowercase letters, numbers, underscores, and hyphens", "deployment_name_format": "Deployment name must start with a lowercase letter, end with a letter or number, and contain only lowercase letters, numbers, underscores, and hyphens", "name_min_length": "Name must be at least 3 characters long", "name_only_letters": "Name can only contain letters (a-z, A-Z)", "name_invalid_format": "Name must contain only letters", "organization_name_empty": "Organization name cannot be empty", "name_with_uppercase_min_length": "Name must be at least 3 characters long", "name_with_uppercase_start_letter": "Name must start with a letter", "name_with_uppercase_end_alphanumeric": "Name must end with a letter or number", "name_with_uppercase_invalid_chars": "Name can only contain letters, numbers, spaces, underscores, and hyphens", "name_with_uppercase_format": "Name must start with a letter, end with a letter or number, and contain only letters, numbers, spaces, underscores, and hyphens", "email_empty": "E-mail field cannot be empty", "email_invalid_format": "Please enter a valid email address", "password_empty": "Password field cannot be empty", "password_too_short": "Password must be at least 8 characters long", "repeat_password_empty": "Please repeat your password", "version_invalid_format": "Version must be in format like '1.0', '1.0.0', 'v1.0', or 'v1.0.0'", "version_empty": "Version cannot be empty", "version_number_invalid": "Version must be a valid number", "checkbox_must_be_checked": "This checkbox must be checked", "version_id_invalid_number": "Version ID must be a valid number", "version_id_must_be_positive": "Version ID must be greater than 0", "version_id_empty": "Version ID is required", "blockchain_config_invalid_number": "Blockchain configuration must be a valid number", "blockchain_config_must_be_positive": "Blockchain configuration ID must be greater than 0", "readable_name_required": "Readable name is required", "readable_name_min_length": "Readable name must be at least 3 characters long", "readable_name_max_length": "Readable name must be at most 100 characters long", "phone_number_required": "Phone number is required", "phone_number_invalid": "Please enter a valid phone number", "phone_number_empty": "Phone number cannot be empty", "number_must_be_positive": "Number must be positive", "required_field": "This field is required", "datetime_required": "Date and time is required", "uri_invalid_format": "Please enter a valid URL"}, "new_issuer": {"title": "Create New Issuer", "create_button": "Create Issuer", "issuer_name_label": "Domain Name", "organization_name_label": "Organization Name", "select_label": "Select", "version_id_label": "Version", "deployment_type_label": "Deployment Type", "blockchain_configuration_id_label": "Blockchain Configuration", "issuer_readable_name_label": "Issuer Name"}, "new_verifier": {"title": "Create New Verifier", "create_button": "Create Verifier", "verifier_name_label": "Domain Name", "organization_name_label": "Organization Name", "version_id_label": "Version", "deployment_type_label": "Deployment Type", "blockchain_configuration_id_label": "Blockchain Configuration", "verifier_readable_name_label": "Verifier Name"}, "new_deployment": {"title": "New Deployment", "description": "Create a new deployment", "new_issuer": "New Issuer", "new_verifier": "New Verifier"}, "dashboard": {"option_bar_back_root": "Dashboard", "option_bar_back_to": "Go back to Dashboard", "logout": "Logout", "issuer_tab": "My Issuers", "verifier_tab": "My Verifiers"}, "copy": {"success": "Key copied to clipboard", "button": "Copy Key"}, "error_modal_status": {"title": "Error", "code": "Code", "close_button": "Close", "unknown": "An unknown error occurred", "get_user_deployments": "Failed to get user deployments", "get_user_data": "Failed to get user data", "delete_deployment": "Failed to delete deployment", "get_deployment_details": "Failed to get deployment details", "user_login": "Failed to login", "user_signup": "Failed to create account", "create_new_issuer": "Failed to create issuer", "create_new_verifier": "Failed to create verifier", "update_deployment_secret": "Failed to upgrade secret", "upgrade_deployment_version": "Failed to upgrade version", "get_available_versions": "Failed to get available versions", "get_env_config": "Failed to get app config", "issuer_admin_add_schema": "Failed to add schema", "schema_not_found": "<PERSON><PERSON><PERSON> not found. The requested schema could not be loaded."}, "modals": {"success_create_new_issuer_title": "Issuer created successfully", "success_create_new_issuer_description": "Please save this key:", "success_create_new_issuer_info_key": "Please securely store this secret. It will not be shown again in the portal. Use this secret in the `Authorization` header as a Bearer token when calling Issuer endpoints.", "success_create_new_issuer_button": "Back", "success_create_new_verifier_title": "Verifier created successfully", "success_create_new_verifier_description": "Please save this key:", "success_create_new_verifier_info_key": "Please securely store this secret. It will not be shown again in the portal. Use this secret in the `Authorization` header as a Bearer token when calling Verifier endpoints.", "success_create_new_verifier_button": "Back", "confirm_update_secret_title": "Are you sure you want to generate a new secret?", "confirm_update_secret_description": "This operation will create a new secret that grants access to this Issuer.", "confirm_update_secret_desctiption_2": "Important: The old secret will be permanently invalidated and can no longer be used. The new secret will be shown only once. Please ensure you copy and securely store it after generation. Ensure you update all endpoints that use this Issuer with the new secret to maintain functionality. ", "confirm_update_secret_button": "Upgrade", "success_update_secret_title": "New Secret Was Generated", "success_update_secret_description": "Please securely store this secret. It will not be shown again in the portal. Use this secret in the `Authorization` header as a Bearer token when calling Deployment endpoints.", "success_update_secret_button": "Close", "confirm_delete_deployment_title": "Delete deployment", "confirm_delete_deployment_description": "To delete a deployment, type its name in the box below", "confirm_delete_deployment_input_label": "Please type: [deployment-name]", "confirm_delete_deployment_input_placeholder": "Type here", "confirm_delete_deployment_button": "Delete", "success_delete_deployment_title": "Deployment deleted successfully", "success_delete_deployment_description": "The deployment has been deleted successfully", "success_delete_deployment_button": "Back", "logic_issuer_upgrade_version_title": "Upgrade Issuer version", "logic_issuer_upgrade_version_description": "Choose the version you want to upgrade to:", "logic_issuer_upgrade_version_button": "Next", "logic_verifier_upgrade_version_title": "Upgrade Verifier version", "logic_verifier_upgrade_version_description": "Choose the version you want to upgrade to:", "logic_verifier_upgrade_version_button": "Next", "confirm_upgrade_version_title": "Upgrade version", "confirm_upgrade_version_description": "Are you sure you want to upgrade the version of this deployment?", "confirm_upgrade_version_button": "Upgrade", "success_upgrade_version_title": "Version upgraded successfully", "success_upgrade_version_description": "The version has been upgraded successfully", "success_upgrade_version_button": "Back", "version_label": "Version", "start_time_label": "Start time", "end_time_label": "End time", "search_label": "Search by phrase", "show_logs_title": "Deployment Logs", "show_logs_description": "This operation will show the server logs for this deployment.", "show_logs_button": "Filter logs", "show_logs_button_clear": "Clear Filters", "show_logs_no_logs": "No logs", "show_logs_button_copy": "Copy logs to clipboard", "show_logs_button_download": "Download logs", "logic_add_schema_title": "Publish Schema", "logic_add_schema_description": "This operation will publish the schema to the issuer instance.", "logic_add_schema_button": "Publish"}, "select_placeholder": "Choose an option", "breadcrumb": {"payment": "Payment", "payment-confirmation": "Waiting for payment confirmation", "dashboard": "One-Click Dashboard", "issuer-details": "Issuer Details", "issuer-admin": "Issuer Admin", "schema-builder": "Schema Builder", "new-issuer": "Create New Issuer", "new-verifier": "Create New Verifier", "settings": "Settings", "profile": "Profile", "verifier-details": "Verifier Details", "logs-list": "Log list", "log-view": "Log view", "signup": "Sign Up", "login": "Log In", "terms-and-conditions": "Terms & Conditions", "policy": "Privacy Policy", "start": "Start", "confirm-email": "Confirm Email", "request-password-reset": "Request Password Reset", "reset-password": "Reset Password"}, "toast": {"success": "Success", "copied_to_clipboard": "Copied to clipboard", "add_new_schema": "New schema added successfully", "schema_imported": "Schema imported successfully", "schema_loaded": "<PERSON><PERSON><PERSON> restored successfully"}, "sidebar": {"no_deployments_available": "You have no available deployments - upgrade your subscription to create more instances"}, "terms": "Terms & Conditions", "termsLabel": "You must accept the terms and conditions", "authorization_label": "Authorization token", "schema_builder": {"define_schema": "Define schema", "name_label": "Name", "description_label": "Description", "description_placeholder": "Description of the attribute", "version_label": "Version", "type_label": "Type", "schema_versions": "Schema Versions", "attribute_name_placeholder": "Attribute name", "title_placeholder_short": "Title", "required_label": "Required", "save_button": "Save", "remove_button": "Remove", "edit_button": "Edit", "nested_attributes": "Nested Attributes", "add_nested_button": "Add Nested", "no_nested_attributes": "No nested attributes", "no_nested_attributes_description": "Click \"Add Nested\" to add attributes to this object", "nested_attributes_colon": "Nested Attributes:", "publish_button": "Publish", "json_preview": "JSON Preview", "json_editor": "JSON", "copy_to_clipboard": "Copy to Clipboard", "credential_subject": "credentialSubject", "no_attributes_added": "No attributes added yet", "no_attributes_description": "Add attributes to see them in the tree view", "add_next_attribute": "Add next attribute", "edit_json": "Edit JSON", "edit_json_description": "Edit your JSON schema directly. The editor provides syntax highlighting, validation, and formatting.", "save_json": "Save JSON", "format_json": "Format JSON", "cancel": "Cancel", "paste_json_here": "Paste your JSON schema here...", "define_schema_button": "Define New Schema"}, "schema_offering": {"title": "Schema Offering Form", "form_description": "Fill out the form to create a credential offering", "submit_button": "Submit Credential", "show_schema": "Show Schema", "hide_schema": "<PERSON><PERSON>", "schema_preview": "Schema Preview", "offerings_list": "Available Offerings", "loading_offerings": "Loading offerings...", "no_offerings": "No offerings available"}}