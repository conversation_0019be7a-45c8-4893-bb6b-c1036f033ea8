apiVersion: apps/v1
kind: Deployment
metadata:
    name: one-click-deployer-front
    namespace: evdi-production-one-click-deployment
spec:
    replicas: 1
    selector:
        matchLabels:
            app: one-click-deployer-front
    template:
        metadata:
            labels:
                app: one-click-deployer-front
        spec:
            containers:
                - name: one-click-deployer-front
                  image: 309596z9.c1.gra9.container-registry.ovh.net/empe/services/one-click-deployer-front:0.1.0-rc6
                  imagePullPolicy: Always
                  ports:
                      - containerPort: 3000
                  env:
                      - name: STRIPE_PUBLISHABLE_KEY
                        value: 'pk_live_51RWgpdFRtYRiDObJKqJDYmsj8u5XCThcsL4D5BOKcRZQ5tLpYnzl9dM9R6I9nOesfHEpYaJSQmG3Ddo6Zon8QVR600uz9T3x66'
                      - name: STRIPE_PRICING_TABLE_ID
                        value: 'prctbl_1RqbW0FRtYRiDObJaHwFe5c4'
                      - name: API_URL
                        value: 'https://deploy-prod.evdi.app/api'
                      - name: DEPLOYMENT_TYPE
                        value: 'EMPE_OVH_K8S_DEPLOYMENT'
            imagePullSecrets:
                - name: harbor-registry-secret
            volumes:
                - name: kubeconfig-volume
                  secret:
                      secretName: kubeconfig-secret
