import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { User } from '@/types/user';

interface State {
    user: User | null;
    setUser: (user: User) => void;
    clearUser: () => void;
}

export const useUserStore = create<State>()(
    persist(
        set => ({
            user: null,
            setUser: async (user: User) => {
                set({ user });
            },
            clearUser: () => {
                set({ user: null });
            },
        }),
        {
            name: 'user-storage', // nazwa klucza w localStorage
            partialize: state => ({
                user: state.user,
            }),
        }
    )
);
