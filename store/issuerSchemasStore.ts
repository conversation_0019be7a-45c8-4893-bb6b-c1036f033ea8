import { create } from 'zustand';
import { IssuerSchema } from '@/types/issuerSchema';

interface State {
    schemas: Array<IssuerSchema>;
    setSchemas: (schemas: Array<IssuerSchema>) => void;
    getSchemasById: (schemaId: string) => IssuerSchema | undefined;
}

export const useIssuerSchemasStore = create<State>((set, get) => ({
    schemas: [],
    setSchemas: (schemas: Array<IssuerSchema>) => {
        console.log('🚀 ~ schemas:', schemas);
        set({ schemas });
    },
    getSchemasById: (schemaId: string): IssuerSchema | undefined => {
        const find = get().schemas.find(schema => schema.id === schemaId);
        console.log('🚀 ~ find:', find);
        return find;
    },
}));
