import { create } from 'zustand';
import { Deployment } from '@/types/deployments';

interface State {
    deploymentDetails: Deployment | null;
    setDeploymentDetails: (deploymentDetails: Deployment) => void;
}

export const useDeploymentDetailsStore = create<State>(set => ({
    deploymentDetails: null,
    setDeploymentDetails: (deploymentDetails: Deployment) => {
        set({ deploymentDetails });
    },
}));
