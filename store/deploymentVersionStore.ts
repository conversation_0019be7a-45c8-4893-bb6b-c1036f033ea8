import { create } from 'zustand';
import { SelectValue } from '@/types/selectValue';

interface State {
    issuerVersion: Array<SelectValue>;
    setIssuerVersion: (issuerVersion: Array<SelectValue>) => void;
    verifierVersion: Array<SelectValue>;
    setVerifierVersion: (verifierVersion: Array<SelectValue>) => void;
}

export const useDeploymentVersionStore = create<State>(set => ({
    issuerVersion: [],
    setIssuerVersion: (issuerVersion: Array<SelectValue>) => {
        set({ issuerVersion });
    },
    verifierVersion: [],
    setVerifierVersion: (verifierVersion: Array<SelectValue>) => {
        set({ verifierVersion });
    },
}));
