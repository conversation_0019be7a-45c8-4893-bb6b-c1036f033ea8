import { REQUIRED_CONFIG_KEYS } from '@/config/envConfig';
import { ErrorGetEnvConfig } from '@/errors/ErrorGetEnvConfig';
import { EnvConfig } from '@/types/configEnv';
import { create } from 'zustand';

interface State {
    env: EnvConfig | Record<string, string>;
    setEnv: (env: EnvConfig) => void;
}

export const useConfigStore = create<State>(set => ({
    env: {},
    setEnv: (env: EnvConfig) => {
        const missingKeys = REQUIRED_CONFIG_KEYS.filter(key => !(key in env));

        if (missingKeys.length > 0) {
            throw new ErrorGetEnvConfig(`Missing required environment variables: ${missingKeys.join(', ')}`);
        }

        set({ env });
    },
}));
