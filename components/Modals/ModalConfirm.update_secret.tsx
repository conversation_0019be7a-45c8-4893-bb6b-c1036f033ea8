import { useTranslations } from 'next-intl';
import { ButtonBorder } from '../Buttons';

interface Props {
    onConfirm: () => void;
}

const LOCALE_KEY = 'confirm_update_secret';

export const ModalConfirmUpdateSecret = ({ onConfirm }: Props) => {
    const t = useTranslations('modals');

    return (
        <>
            <h2 className="text-2xl font-bold text-white">{t(`${LOCALE_KEY}_title`)}</h2>
            <p className="text-white text-center">{t(`${LOCALE_KEY}_description`)}</p>
            <p className="text-white text-center font-semibold">{t(`${LOCALE_KEY}_desctiption_2`)}</p>
            <ButtonBorder id="components_Modals_ModalConfirm.update_secret_button_t6zhit" onClick={onConfirm}>{t(`${LOCALE_KEY}_button`)}</ButtonBorder>
        </>
    );
};
