import { useTranslations } from 'next-intl';
import { ButtonGradient } from '../Buttons';
import { useUpgradeVerifierVersion } from '@/hooks/useUpgradeVerifierVersion';
import { renderFormFields } from '@/common/renderFormFields';
import { UpgradeVerifierVersionFormInputsTypes } from '@/validation/upgradeVerifierVersionValidation';
import { DeploymentType } from '@/types/deployments';
import { useGetAvailableVersions } from '@/hooks/useGetAvailableVersions';
import { LoaderSpinner } from '../Loaders';

interface Props {
    deploymentId: string;
    deploymentCurrentVersion: string;
}

const LOCALE_KEY = 'logic_verifier_upgrade_version';

export const ModalLogicVerifierUpgradeVersion = ({ deploymentId, deploymentCurrentVersion }: Props) => {
    const t = useTranslations('modals');
    const { trigger, formFields, register, handleSubmit, errors, isValid, onSubmit, isSubmitting, control } =
        useUpgradeVerifierVersion({ deploymentId });
    const { versions } = useGetAvailableVersions({
        type: DeploymentType.VERIFIER,
        deploymentCurrentVersion,
    });

    if (!versions.length) {
        return (
            <div>
                <LoaderSpinner />
            </div>
        );
    }

    const fieldsToRender = renderFormFields({
        formFields,
        register,
        errors,
        t,
        formInputsTypes: UpgradeVerifierVersionFormInputsTypes,
        trigger,
        control,
        selectOptions: {
            version: versions,
        },
    });

    return (
        <form onSubmit={handleSubmit(onSubmit)} className="flex text-left flex-col gap-4 p-6">
            <h2 className="text-2xl text-center font-bold text-white">{t(`${LOCALE_KEY}_title`)}</h2>
            <p className="text-white text-center">{t(`${LOCALE_KEY}_description`)}</p>
            {fieldsToRender}
            <ButtonGradient id="components_Modals_ModalLogic.verifier.upgrade_version_button_24klwz" disabled={!isValid} isLoading={isSubmitting}>
                {t(`${LOCALE_KEY}_button`)}
            </ButtonGradient>
        </form>
    );
};
