import { useTranslations } from 'next-intl';
import { ButtonBorder } from '../Buttons';
import { TextInput } from '../FormFields';
import { useState } from 'react';

interface Props {
    onConfirm: () => void;
    deploymentName: string;
}

const LOCALE_KEY = 'confirm_delete_deployment';

export const ModalConfirmDeleteDeployment = ({ onConfirm, deploymentName }: Props) => {
    const t = useTranslations('modals');
    const [inputValue, setInputValue] = useState('');

    return (
        <>
            <h2 className="text-2xl font-bold text-white">{t(`${LOCALE_KEY}_title`)}</h2>
            <p className="text-white text-center">{t(`${LOCALE_KEY}_description`)}</p>
            <TextInput
                placeholder={t(`${LOCALE_KEY}_input_placeholder`)}
                value={inputValue}
                onChange={e => setInputValue(e.target.value)}
                labelNode={
                    <span
                        className="text-center text-sm"
                        dangerouslySetInnerHTML={{
                            __html: t(`${LOCALE_KEY}_input_label`).replace(
                                '[deployment-name]',
                                `<span style="color: #3b82f6; font-weight: 600;">${deploymentName}</span>`
                            ),
                        }}
                    />
                }
            />
            <ButtonBorder id="components_Modals_ModalConfirm.delete_deployment_button_h9bv8h" onClick={onConfirm} disabled={inputValue !== deploymentName}>
                {t(`${LOCALE_KEY}_button`)}
            </ButtonBorder>
        </>
    );
};
