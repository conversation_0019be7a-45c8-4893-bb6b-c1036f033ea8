import { DeploymentType } from '@/types/deployments';
import { DashboardTableTabsButtons } from './DashboardTableTabsButtons';
import { useTranslations } from 'next-intl';

interface Props {
    selectedDeploymentType: DeploymentType;
    setSelectedDeploymentType: (type: DeploymentType) => void;
}

export const DashboardTableTabs = ({ selectedDeploymentType, setSelectedDeploymentType }: Props) => {
    const t = useTranslations();

    const handleSetIssuerType = () => {
        setSelectedDeploymentType(DeploymentType.ISSUER);
    };

    const handleSetVerifierType = () => {
        setSelectedDeploymentType(DeploymentType.VERIFIER);
    };

    return (
        <div
            role="tablist"
            aria-label="Dashboard Views"
            className="flex flex-row gap-2 justify-end absolute top-[-30px] left-0"
        >
            <DashboardTableTabsButtons
                aria-controls="issuer-panel"
                selected={selectedDeploymentType === DeploymentType.ISSUER}
                onClick={handleSetIssuerType}
            >
                <span>{t('dashboard.issuer_tab')}</span>
            </DashboardTableTabsButtons>
            <DashboardTableTabsButtons
                aria-controls="verifier-panel"
                selected={selectedDeploymentType === DeploymentType.VERIFIER}
                onClick={handleSetVerifierType}
            >
                <span>{t('dashboard.verifier_tab')}</span>
            </DashboardTableTabsButtons>
        </div>
    );
};
