import clsx from 'clsx';

interface Props extends React.ButtonHTMLAttributes<HTMLButtonElement> {
    children?: React.ReactNode;
    selected: boolean;
}

export const DashboardTableTabsButtons = ({ children, selected, ...props }: Props) => {
    return (
        <button id="components_DashboardTableTabs_DashboardTableTabsButtons_button_clgwd3"
            role="tab"
            aria-selected={selected}
            className={clsx(
                'disabled:opacity-50 w-full text-center whitespace-nowrap text-main-600 pb-2 pt-1 px-6  overflow-hidden rounded-md',
                !selected ? 'opacity-50 border-[1px] border-main-800' : 'border-gradient'
            )}
            {...props}
        >
            {children ?? 'Click'}
        </button>
    );
};
