import type { Meta, StoryObj } from '@storybook/react';
import { DashboardTableTabsButtons } from './DashboardTableTabsButtons';

const meta: Meta<typeof DashboardTableTabsButtons> = {
    title: 'Components/DashboardTableTabs/DashboardTableTabsButtons',
    component: DashboardTableTabsButtons,
    tags: ['autodocs'],
    decorators: [
        Story => (
            <div className="bg-main-400 text-white">
                <Story />
            </div>
        ),
    ],
    argTypes: {
        selected: {
            control: 'boolean',
            description: '<PERSON><PERSON><PERSON><PERSON>, czy przycisk jest aktualnie wybrany',
        },
        children: {
            control: 'text',
            description: '<PERSON>awartość przycisku',
        },
        onClick: { action: 'clicked' },
    },
};

export default meta;
type Story = StoryObj<typeof DashboardTableTabsButtons>;

export const Selected: Story = {
    args: {
        selected: true,
        children: 'Wybrany Tab',
    },
};

export const NotSelected: Story = {
    args: {
        selected: false,
        children: 'Niewybrany Tab',
    },
};

export const Disabled: Story = {
    args: {
        selected: false,
        children: 'Wyłączony Tab',
        disabled: true,
    },
};

export const WithoutChildren: Story = {
    args: {
        selected: false,
    },
};
