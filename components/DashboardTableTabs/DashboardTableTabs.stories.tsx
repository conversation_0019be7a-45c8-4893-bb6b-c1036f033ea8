import type { Meta, StoryObj } from '@storybook/react';
import { DashboardTableTabs } from './DashboardTableTabs';
import { DeploymentType } from '@/types/deployments';
import { useState } from 'react';
import { messages } from '@/.storybook/i18n-mock';
import { NextIntlClientProvider } from 'next-intl';

const meta: Meta<typeof DashboardTableTabs> = {
    title: 'Components/DashboardTableTabs',
    component: DashboardTableTabs,
    parameters: {
        layout: 'centered',
    },
    decorators: [
        Story => (
            <NextIntlClientProvider messages={messages} locale="en">
                <div className="bg-main-400 p-8 w-screen h-screen">
                    <div
                        style={{
                            padding: '6rem',
                            position: 'relative',
                            minHeight: '100px',
                        }}
                    >
                        <Story />
                    </div>
                </div>
            </NextIntlClientProvider>
        ),
    ],
};

export default meta;
type Story = StoryObj<typeof DashboardTableTabs>;

const DashboardTableTabsWithState = () => {
    const [selectedType, setSelectedType] = useState<DeploymentType>(DeploymentType.ISSUER);

    return <DashboardTableTabs selectedDeploymentType={selectedType} setSelectedDeploymentType={setSelectedType} />;
};

export const Default: Story = {
    render: () => <DashboardTableTabsWithState />,
};
