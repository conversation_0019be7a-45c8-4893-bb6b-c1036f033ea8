import type { Meta, StoryObj } from '@storybook/react';
import { ButtonBorderBasic } from './ButtonBorderBasic';

const meta: Meta<typeof ButtonBorderBasic> = {
    title: 'Components/Buttons/ButtonBorderBasic',
    component: ButtonBorderBasic,
    tags: ['autodocs'],
    argTypes: {
        onClick: { action: 'clicked' },
        children: {
            control: 'text',
            description: 'Zawartość przycisku',
        },
    },
};

export default meta;
type Story = StoryObj<typeof ButtonBorderBasic>;

export const Default: Story = {
    args: {
        children: 'Click',
    },
};

export const WithCustomText: Story = {
    args: {
        children: 'Custom Button Text',
    },
};

export const Disabled: Story = {
    args: {
        children: 'Disabled Button',
        disabled: true,
    },
};
