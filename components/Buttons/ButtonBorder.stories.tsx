import type { Meta, StoryObj } from '@storybook/react';
import { ButtonBorder } from './ButtonBorder';

const meta: Meta<typeof ButtonBorder> = {
    title: 'Components/Buttons/ButtonBorder',
    component: ButtonBorder,
    tags: ['autodocs'],
    argTypes: {
        children: {
            control: 'text',
            description: 'Zawartość przycisku',
        },
        disabled: {
            control: 'boolean',
            description: '<PERSON> w<PERSON> przycisku',
        },
        onClick: { action: 'clicked' },
    },
    decorators: [
        Story => (
            <div className=" bg-main-700 p-8">
                <Story />
            </div>
        ),
    ],
};

export default meta;
type Story = StoryObj<typeof ButtonBorder>;

export const Default: Story = {
    args: {
        children: 'Click me',
    },
};

export const Disabled: Story = {
    args: {
        children: 'Disabled Button',
        disabled: true,
    },
};

export const WithoutChildren: Story = {
    args: {},
};

export const LongText: Story = {
    args: {
        children: 'This is a button with very long text',
    },
};
