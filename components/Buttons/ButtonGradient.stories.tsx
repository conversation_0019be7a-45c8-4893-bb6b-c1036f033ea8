import type { Meta, StoryObj } from '@storybook/react';
import { ButtonGradient } from './ButtonGradient';

const meta = {
    title: 'Components/Buttons/ButtonGradient',
    component: ButtonGradient,
    parameters: {
        layout: 'centered',
    },
    tags: ['autodocs'],
} satisfies Meta<typeof ButtonGradient>;

export default meta;
type Story = StoryObj<typeof ButtonGradient>;

export const Default: Story = {
    args: {
        children: 'Button',
    },
};

export const WithoutChildren: Story = {
    args: {},
};

export const Disabled: Story = {
    args: {
        children: 'Disabled Button',
        disabled: true,
    },
};

export const WithOnClick: Story = {
    args: {
        children: 'Click me',
        onClick: () => alert('Button clicked!'),
    },
};
