import clsx from 'clsx';
import { LoaderSpinnerSmall } from '../Loaders';

interface Props extends React.ButtonHTMLAttributes<HTMLButtonElement> {
    children?: React.ReactNode;
    isLoading?: boolean;
}

export const ButtonBorderBasic = ({ children, isLoading, className, ...props }: Props) => {
    return (
        <button id="components_Buttons_ButtonBorderBasic_button_viwo7u"
            className={clsx(
                className,
                'border border-white whitespace-nowrap text-sm rounded-md px-5 py-3 font-bold w-full text-center'
            )}
            {...props}
        >
            {isLoading ? <LoaderSpinnerSmall /> : (children ?? 'Click')}
        </button>
    );
};
