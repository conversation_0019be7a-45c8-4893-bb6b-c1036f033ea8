import type { Meta, StoryObj } from '@storybook/react';
import { ButtonBorderSmall } from './ButtonBorderSmall';

const meta: Meta<typeof ButtonBorderSmall> = {
    title: 'Components/Buttons/ButtonBorderSmall',
    component: ButtonBorderSmall,
    parameters: {
        layout: 'centered',
    },
    tags: ['autodocs'],
    decorators: [
        Story => (
            <div className="bg-main-400 p-4">
                <Story />
            </div>
        ),
    ],
};

export default meta;
type Story = StoryObj<typeof ButtonBorderSmall>;

export const Default: Story = {
    args: {
        children: 'Click me',
    },
};

export const Disabled: Story = {
    args: {
        children: 'Disabled But<PERSON>',
        disabled: true,
    },
};

export const WithCustomText: Story = {
    args: {
        children: 'Custom Text',
    },
};

export const WithOnClick: Story = {
    args: {
        children: 'Click Event',
        onClick: () => alert('Button clicked!'),
    },
};
