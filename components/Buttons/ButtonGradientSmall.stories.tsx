import type { Meta, StoryObj } from '@storybook/react';
import { ButtonGradientSmall } from './ButtonGradientSmall';

const meta: Meta<typeof ButtonGradientSmall> = {
    title: 'Components/Buttons/ButtonGradientSmall',
    component: ButtonGradientSmall,
    tags: ['autodocs'],
    argTypes: {
        children: {
            control: 'text',
            description: 'Zawartość przycisku',
        },
        disabled: {
            control: 'boolean',
            description: 'Stan wyłączenia przycisku',
        },
    },
};

export default meta;
type Story = StoryObj<typeof ButtonGradientSmall>;

export const Default: Story = {
    args: {
        children: 'Click',
    },
};

export const WithCustomText: Story = {
    args: {
        children: 'Custom Button',
    },
};

export const Disabled: Story = {
    args: {
        children: 'Disabled Button',
        disabled: true,
    },
};
