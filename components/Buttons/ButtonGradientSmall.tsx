import { LoaderSpinnerSmall } from '../Loaders';

interface Props extends React.ButtonHTMLAttributes<HTMLButtonElement> {
    children?: React.ReactNode;
    isLoading?: boolean;
}

export const ButtonGradientSmall = ({ children, isLoading, ...props }: Props) => {
    return (
        <button id="components_Buttons_ButtonGradientSmall_button_9xqyg9"
            className={
                'item-gradient disabled:opacity-50 text-sm disabled:cursor-not-allowed w-full text-center whitespace-nowrap py-1 text-white px-6 rounded-xl'
            }
            {...props}
        >
            {isLoading ? <LoaderSpinnerSmall /> : (children ?? 'Click')}
        </button>
    );
};
