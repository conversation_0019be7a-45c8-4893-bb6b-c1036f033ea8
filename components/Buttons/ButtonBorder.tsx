import { LoaderSpinnerSmall } from '../Loaders';

interface Props extends React.ButtonHTMLAttributes<HTMLButtonElement> {
    children?: React.ReactNode;
    isLoading?: boolean;
}

export const ButtonBorder = ({ children, isLoading, ...props }: Props) => {
    return (
        <button id="components_Buttons_ButtonBorder_button_4ube3k"
            className="border-gradient text-sm disabled:opacity-50 disabled:cursor-not-allowed w-full text-center whitespace-nowrap text-main-600 py-3 px-6 rounded-md"
            {...props}
        >
            {isLoading ? <LoaderSpinnerSmall /> : (children ?? 'Click')}
        </button>
    );
};
