import { LoaderSpinnerSmall } from '../Loaders';

interface Props extends React.ButtonHTMLAttributes<HTMLButtonElement> {
    children?: React.ReactNode;
    isLoading?: boolean;
}

export const ButtonBorderSmall = ({ children, isLoading, ...props }: Props) => {
    return (
        <button id="components_Buttons_ButtonBorderSmall_button_8hjjyv"
            className="border-gradient text-sm disabled:opacity-50 whitespace-nowrap disabled:cursor-not-allowed w-full text-center text-main-600 px-6 py-1  rounded-xl"
            {...props}
        >
            {isLoading ? <LoaderSpinnerSmall /> : (children ?? 'Click')}
        </button>
    );
};
