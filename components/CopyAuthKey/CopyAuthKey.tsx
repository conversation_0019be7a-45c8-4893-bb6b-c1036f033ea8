'use client';
import { useToast } from '@/hooks/useToast';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import { SuccessToast } from '../Toasts';

interface Props {
    authKey: string;
}

export const CopyAuthKey = ({ authKey }: Props) => {
    const t = useTranslations('copy');
    const { showToast } = useToast();

    const copyToClipboard = () => {
        navigator.clipboard.writeText(authKey).then(() => {
            showToast(<SuccessToast title={t('success')} message={authKey} />);
        });
    };

    return (
        <div className="flex flex-row gap-2">
            <div className="text-sm border-main-1100 border px-4 flex items-center rounded-lg backdrop-blur-2xl">
                {authKey}
            </div>
            <button id="components_CopyAuthKey_CopyAuthKey_button_eksgkr"
                onClick={copyToClipboard}
                className="border-main-1100 border flex justify-center items-center w-10 h-10 p-2 rounded-lg backdrop-blur-2xl"
            >
                <Image src="/icons/copy.svg" alt="Copy Icon" width={28} height={28} priority />
            </button>
        </div>
    );
};
