import type { <PERSON>a, StoryObj } from '@storybook/react';
import { BgGradient } from './BgGradient';

const meta: Meta<typeof BgGradient> = {
    title: 'Components/BgGradient',
    component: BgGradient,
    parameters: {
        layout: 'fullscreen',
    },
};

export default meta;
type Story = StoryObj<typeof BgGradient>;

export const Default: Story = {
    render: () => (
        <div className="overflow-hidden h-screen w-full relative">
            <BgGradient />
        </div>
    ),
};
