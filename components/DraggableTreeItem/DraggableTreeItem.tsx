'use client';

import { SchemaAttribute } from '@/types/schemaManager';
import { useDraggable, useDroppable } from '@dnd-kit/core';

interface DraggableTreeItemProps {
    attribute: SchemaAttribute;
    depth: number;
    isEditing: boolean;
    onEdit: (id: string) => void;
    onRemove: (id: string) => void;
    isValidDropTarget: (draggedId: string, targetParentId: string | null) => boolean;
    children?: React.ReactNode;
}

export const DraggableTreeItem = ({
    attribute,
    depth,
    isEditing,
    onEdit,
    isValidDropTarget,
    children,
}: DraggableTreeItemProps) => {
    const marginLeft = depth * 24; // 24px per level of nesting

    const {
        attributes: draggableAttributes,
        listeners,
        setNodeRef: setDraggableRef,
        transform,
        isDragging,
    } = useDraggable({
        id: attribute.id,
        data: {
            type: 'attribute',
            attribute,
        },
    });

    const { setNodeRef: setDroppableRef, active } = useDroppable({
        id: `droppable-${attribute.id}`,
        data: {
            type: 'attribute-container',
            parentId: attribute.dataType === 'object' ? attribute.id : null,
        },
    });

    const FolderIcon = () => (
        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" className="text-main-600">
            <path
                d="M1.5 3.5C1.5 2.67157 2.17157 2 3 2H6.5L8 3.5H13C13.8284 3.5 14.5 4.17157 14.5 5V12.5C14.5 13.3284 13.8284 14 13 14H3C2.17157 14 1.5 13.3284 1.5 12.5V3.5Z"
                stroke="currentColor"
                strokeWidth="1.2"
                fill="none"
            />
        </svg>
    );

    const FileIcon = () => (
        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" className="text-main-800">
            <path
                d="M3 2C2.44772 2 2 2.44772 2 3V13C2 13.5523 2.44772 14 3 14H13C13.5523 14 14 13.5523 14 13V6L10 2H3Z"
                stroke="currentColor"
                strokeWidth="1.2"
                fill="none"
            />
            <path d="M10 2V6H14" stroke="currentColor" strokeWidth="1.2" fill="none" />
        </svg>
    );

    const DragHandle = () => (
        <svg width="12" height="12" viewBox="0 0 12 12" fill="none" className="text-main-200">
            <circle cx="3" cy="3" r="1" fill="currentColor" />
            <circle cx="9" cy="3" r="1" fill="currentColor" />
            <circle cx="3" cy="6" r="1" fill="currentColor" />
            <circle cx="9" cy="6" r="1" fill="currentColor" />
            <circle cx="3" cy="9" r="1" fill="currentColor" />
            <circle cx="9" cy="9" r="1" fill="currentColor" />
        </svg>
    );

    // Check if current drop target is valid
    const isValidDrop =
        active &&
        active.id !== attribute.id &&
        (attribute.dataType === 'object' ? isValidDropTarget(active.id as string, attribute.id) : false);

    const canDropToObject = attribute.dataType === 'object' && isValidDrop;

    const style = transform
        ? {
              transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,
              zIndex: isDragging ? 1000 : 'auto',
              opacity: isDragging ? 0.5 : 1,
          }
        : undefined;

    return (
        <div key={attribute.id} style={style}>
            <div
                ref={setDraggableRef}
                className={`flex items-center gap-2 py-1 px-2 rounded cursor-pointer transition-colors relative ${
                    isEditing ? 'bg-main-500/20 text-main-100' : 'hover:bg-main-800/30 text-main-600'
                } ${canDropToObject ? 'ring-2 ring-main-200 bg-main-200/10' : ''} ${
                    isDragging ? 'opacity-50 shadow-lg' : ''
                }`}
                style={{ marginLeft: `${marginLeft}px` }}
                onClick={() => onEdit(attribute.id)}
            >
                {/* Drag Handle */}
                <div
                    {...draggableAttributes}
                    {...listeners}
                    className="cursor-grab active:cursor-grabbing flex items-center justify-center p-1 hover:bg-main-700/20 rounded"
                    onClick={e => e.stopPropagation()}
                >
                    <DragHandle />
                </div>

                {/* Icon */}
                {attribute.dataType === 'object' && attribute.properties && attribute.properties.length > 0 ? (
                    <FolderIcon />
                ) : (
                    <FileIcon />
                )}

                {/* Content */}
                <div className="flex-1 min-w-0">
                    <div className="text-sm font-medium truncate">{attribute.title || attribute.name}</div>
                    <div className="text-xs text-main-800 truncate">
                        {attribute.name} ({attribute.dataType})
                        {attribute.required && <span className="text-red-400 ml-1">*</span>}
                    </div>
                </div>

                {/* Drop indicator for object types */}
                {canDropToObject && (
                    <div className="absolute inset-0 border-2 border-dashed border-main-200 rounded bg-main-200/5 pointer-events-none" />
                )}
            </div>

            {/* Droppable area - always present but only active for object types */}
            <div ref={setDroppableRef} className="relative">
                {attribute.dataType === 'object' && (
                    <>
                        {children}
                        {canDropToObject && <div className="absolute inset-0 bg-main-200/5 pointer-events-none" />}
                    </>
                )}
            </div>
        </div>
    );
};
