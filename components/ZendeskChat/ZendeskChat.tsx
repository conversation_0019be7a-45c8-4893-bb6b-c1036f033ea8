'use client';
import { useEffect } from 'react';
import Script from 'next/script';
import { useAuthStore } from '@/store/authStore';

export const ZendeskChat = () => {
    const { zendeskToken } = useAuthStore();

    useEffect(() => {
        const identifyUser = async () => {
            if (!zendeskToken) {
                console.warn('Missing zendeskToken');
                return;
            }

            try {
                const waitForZendesk = (attempts = 50, interval = 100) => {
                    if (typeof window.zE !== 'function') {
                        if (attempts <= 0) {
                            console.error('Zendesk script failed to load');
                            return;
                        }
                        setTimeout(() => waitForZendesk(attempts - 1, interval), interval);
                        return;
                    }

                    window.zE('messenger', 'show');
                    // eslint-disable-next-line @typescript-eslint/no-explicit-any
                    window.zE('messenger', 'loginUser', function (cb: any) {
                        cb(zendeskToken);
                    });
                };

                waitForZendesk();
            } catch (error) {
                console.error('Failed to identify user in Zendesk:', error);
            }
        };

        const logoutChat = async () => {
            if (window.zE) {
                window.zE('messenger', 'close');
                window.zE('messenger', 'logoutUser');
                window.zE('messenger', 'hide');
            }
        };

        if (zendeskToken) {
            identifyUser();
        }

        if (!zendeskToken) {
            logoutChat();
        }
    }, [zendeskToken]);

    return (
        <Script
            id="ze-snippet"
            src="https://static.zdassets.com/ekr/snippet.js?key=bbb92d84-74f1-41d0-a90d-74bd558bbc72"
        />
    );
};
