'use client';

import { useDroppable } from '@dnd-kit/core';

interface DroppableAreaProps {
    id: string;
    parentId: string | null;
    insertIndex: number;
    depth: number;
    isValidDropTarget: (draggedId: string, targetParentId: string | null) => boolean;
    children?: React.ReactNode;
    showEmptyState?: boolean;
}

export const DroppableArea = ({
    id,
    parentId,
    insertIndex,
    depth,
    isValidDropTarget,
    children,
    showEmptyState = false,
}: DroppableAreaProps) => {
    const { setNodeRef, isOver, active } = useDroppable({
        id,
        data: {
            type: 'drop-area',
            parentId,
            insertIndex,
        },
    });

    const isValidDrop = active && active.id !== id && isValidDropTarget(active.id as string, parentId);

    const marginLeft = depth * 24; // 24px per level of nesting

    return (
        <div
            ref={setNodeRef}
            className={`transition-all duration-200 min-h-[8px] ${
                isOver && isValidDrop
                    ? 'bg-main-200/10 border-1 border-dashed border-main-200'
                    : 'border-1 border-transparent'
            } ${
                showEmptyState && !children && insertIndex === 0 ? 'min-h-[32px] border-dashed border-main-800/30' : ''
            }`}
            style={{ marginLeft: `${marginLeft}px` }}
        >
            {isOver && isValidDrop && <div className="h-2 bg-main-200 rounded-full mx-2" />}
            {showEmptyState && !children && insertIndex === 0 && !active && (
                <div className="flex items-center justify-center h-8 text-xs text-main-800/60">Drop items here</div>
            )}
            {children}
        </div>
    );
};
