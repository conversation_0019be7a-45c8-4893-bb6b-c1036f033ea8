import { DeploymentStatus, DeploymentStatusIcons } from '@/types/deployments';
import { motion, Variants } from 'motion/react';
import Image from 'next/image';

interface Props {
    status: DeploymentStatus;
}

const initializingStatuses = [
    DeploymentStatus.READY_TO_PUT_TO_BLOCKCHAIN,
    DeploymentStatus.TOKEN_ON_ISSUER,
    DeploymentStatus.INIT,
    DeploymentStatus.PENDING,
];

const AnimatedDots = () => {
    const dotVariants: Variants = {
        pulse: {
            scale: [1, 1.5, 1],
            transition: {
                duration: 1.2,
                repeat: Infinity,
                ease: 'easeInOut',
            },
        },
    };

    return (
        <motion.div
            animate="pulse"
            className="flex flex-row gap-1 pb-1"
            transition={{ staggerChildren: -0.2, staggerDirection: -1 }}
        >
            <motion.div className="rounded-full bg-main-600 w-0.5 h-0.5" variants={dotVariants} />
            <motion.div className="rounded-full bg-main-600 w-0.5 h-0.5" variants={dotVariants} />
            <motion.div className="rounded-full bg-main-600 w-0.5 h-0.5" variants={dotVariants} />
        </motion.div>
    );
};

const formatStatusText = (status: string): string => {
    return status
        .toLowerCase()
        .replace(/_/g, ' ')
        .replace(/\b\w/g, char => char.toUpperCase());
};

export const TableItemStatus = ({ status }: Props) => {
    const isInitializing = initializingStatuses.includes(status);

    return (
        <div className="flex items-center justify-start gap-2">
            <Image src={DeploymentStatusIcons[status]} alt="status" width={16} height={16} />
            <span>
                {isInitializing ? (
                    <div className="flex flex-row gap-1 items-end">
                        Initializing
                        <AnimatedDots />
                    </div>
                ) : (
                    formatStatusText(status)
                )}
            </span>
        </div>
    );
};
