import type { Meta, StoryObj } from '@storybook/react';
import { TableItem } from './TableItem';
import { DeploymentStatus } from '@/types/deployments';
import { NextIntlClientProvider } from 'next-intl';
import { messages } from '../../.storybook/i18n-mock';
import { DeploymentsTypesEnum } from '@/common/deploymentsTypes';
import { BlockchainNetworkName } from '@/types/blockchanConfigs';

const meta: Meta<typeof TableItem> = {
    title: 'Widgets/WidgetDeploymentsTable/TableItem',
    component: TableItem,
    parameters: {
        layout: 'centered',
        nextjs: {
            appDirectory: true,
        },
    },
    decorators: [
        Story => (
            <NextIntlClientProvider messages={messages} locale="en">
                <table className="bg-main-400 text-white">
                    <tbody>
                        <Story />
                    </tbody>
                </table>
            </NextIntlClientProvider>
        ),
    ],
};

export default meta;
type Story = StoryObj<typeof TableItem>;

export const Default: Story = {
    args: {
        deployment: {
            id: 1,
            issuerName: 'Example Corp',
            fullHost: 'example.corp.com',
            status: DeploymentStatus.ACTIVE,
            userId: 1,
            deploymentType: DeploymentsTypesEnum.EMPE_MOCK_DEPLOYMENT,
            networkName: 'MAINNET' as BlockchainNetworkName,
            version: '1.0.0',
        },
    },
};

export const PendingStatus: Story = {
    args: {
        deployment: {
            id: 2,
            issuerName: 'Test Company',
            fullHost: 'staging.test.com',
            status: DeploymentStatus.PENDING,
            userId: 1,
            deploymentType: DeploymentsTypesEnum.EMPE_MOCK_DEPLOYMENT,
            networkName: 'TESTNET' as BlockchainNetworkName,
            version: '1.2.0',
        },
    },
};

export const FailedStatus: Story = {
    args: {
        deployment: {
            id: 3,
            issuerName: 'Dev Ltd',
            fullHost: 'dev.example.com',
            status: DeploymentStatus.FAILED,
            userId: 2,
            deploymentType: DeploymentsTypesEnum.EMPE_MOCK_DEPLOYMENT,
            networkName: 'TESTNET' as BlockchainNetworkName,
            version: '0.9.5',
        },
    },
};
