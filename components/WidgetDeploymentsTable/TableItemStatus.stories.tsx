import type { Meta, StoryObj } from '@storybook/react';
import { TableItemStatus } from './TableItemStatus';
import { DeploymentStatus } from '@/types/deployments';
import { NextIntlClientProvider } from 'next-intl';
import { messages } from '@/.storybook/i18n-mock';

const meta: Meta<typeof TableItemStatus> = {
    title: 'Widgets/WidgetDeploymentsTable/TableItemStatus',
    component: TableItemStatus,
    parameters: {
        layout: 'centered',
        nextjs: {
            appDirectory: true,
        },
    },
    decorators: [
        Story => (
            <NextIntlClientProvider messages={messages} locale="en">
                <Story />
            </NextIntlClientProvider>
        ),
    ],
};

export default meta;
type Story = StoryObj<typeof TableItemStatus>;

export const Active: Story = {
    args: {
        status: DeploymentStatus.ACTIVE,
    },
};

export const Pending: Story = {
    args: {
        status: DeploymentStatus.PENDING,
    },
};

export const Failed: Story = {
    args: {
        status: DeploymentStatus.FAILED,
    },
};

export const Inactive: Story = {
    args: {
        status: DeploymentStatus.INIT,
    },
};
