import type { Meta, StoryObj } from '@storybook/react';
import { WidgetDeploymentsTable } from './WidgetDeploymentsTable';
import { DeploymentStatus, type Deployment } from '@/types/deployments';
import { NextIntlClientProvider } from 'next-intl';
import { messages } from '@/.storybook/i18n-mock';
import { BlockchainNetworkName } from '@/types/blockchanConfigs';
import { DeploymentsTypesEnum } from '@/common/deploymentsTypes';

const meta: Meta<typeof WidgetDeploymentsTable> = {
    title: 'Widgets/WidgetDeploymentsTable',
    component: WidgetDeploymentsTable,
    tags: ['autodocs'],
    parameters: {
        nextjs: {
            appDirectory: true,
        },
    },
    decorators: [
        Story => (
            <NextIntlClientProvider messages={messages} locale="en">
                <div className="bg-main-400 text-white">
                    <Story />
                </div>
            </NextIntlClientProvider>
        ),
    ],
};

export default meta;
type Story = StoryObj<typeof WidgetDeploymentsTable>;

const mockDeployments: Deployment[] = [
    {
        id: 1,
        issuerName: '<PERSON> Doe',
        fullHost: 'prod.example.com',
        status: DeploymentStatus.ACTIVE,
        userId: 101,
        deploymentType: DeploymentsTypesEnum.EMPE_MOCK_DEPLOYMENT,
        networkName: 'MAINNET' as BlockchainNetworkName,
        version: '1.0.0',
    },
    {
        id: 2,
        issuerName: 'Jane Smith',
        fullHost: 'staging.example.com',
        status: DeploymentStatus.FAILED,
        userId: 102,
        deploymentType: DeploymentsTypesEnum.EMPE_MOCK_DEPLOYMENT,
        networkName: 'TESTNET' as BlockchainNetworkName,
        version: '1.2.3',
    },
    {
        id: 3,
        issuerName: 'Mike Johnson',
        fullHost: 'dev.example.com',
        status: DeploymentStatus.PENDING,
        userId: 103,
        deploymentType: DeploymentsTypesEnum.EMPE_MOCK_DEPLOYMENT,
        networkName: 'TESTNET' as BlockchainNetworkName,
        version: '0.9.0',
    },
    {
        id: 4,
        issuerName: 'Sarah Wilson',
        fullHost: 'test.example.com',
        status: DeploymentStatus.INIT,
        userId: 104,
        deploymentType: DeploymentsTypesEnum.EMPE_MOCK_DEPLOYMENT,
        networkName: 'TESTNET' as BlockchainNetworkName,
        version: '0.1.0-beta',
    },
];

export const Default: Story = {
    args: {
        deployments: mockDeployments,
    },
};
