import type { Meta, StoryObj } from '@storybook/react';
import { EmptyTableInfo } from './EmptyTableInfo';
import { NextIntlClientProvider } from 'next-intl';
import { messages } from '@/.storybook/i18n-mock';

const meta: Meta<typeof EmptyTableInfo> = {
    title: 'Widgets/EmptyTableInfo',
    component: EmptyTableInfo,
    decorators: [
        Story => (
            <NextIntlClientProvider messages={messages} locale="en">
                <div className="bg-main-400 text-white">
                    <Story />
                </div>
            </NextIntlClientProvider>
        ),
    ],
};

export default meta;
type Story = StoryObj<typeof EmptyTableInfo>;

export const Default: Story = {};
