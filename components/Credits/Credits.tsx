import { useTranslations } from 'next-intl';

interface Props {
    credits: number;
}
export const Credits = ({ credits }: Props) => {
    const t = useTranslations();
    return (
        <div className="bg-main-500 rounded-full h-6 text-main-600 flex flex-row items-center gap-2 pr-4">
            <div className="item-gradient flex flex-row justify-center items-center font-semibold text-sm rounded-full h-6 w-6">
                {credits}
            </div>
            <span className="font-regular text-sm whitespace-nowrap">{t('credits.credits_left')}</span>
        </div>
    );
};
