import type { Meta, StoryObj } from '@storybook/react';
import { LoaderSpinnerSmall } from './LoaderSpinnerSmall';

const meta: Meta<typeof LoaderSpinnerSmall> = {
    title: 'Components/Loaders/LoaderSpinnerSmall',
    component: LoaderSpinnerSmall,
    parameters: {
        layout: 'centered',
    },
    tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof LoaderSpinnerSmall>;

export const Default: Story = {
    args: {},
};

// Możesz dodać więcej wariantów, jeśli będą potrzebne
export const InContainer: Story = {
    decorators: [
        Story => (
            <div style={{ padding: '20px', background: '#2d2d2d' }}>
                <Story />
            </div>
        ),
    ],
};
