import type { Meta, StoryObj } from '@storybook/react';
import { PageSizeLoader } from './PageSizeLoader';

const meta: Meta<typeof PageSizeLoader> = {
    title: 'Components/Loaders/PageSizeLoader',
    component: PageSizeLoader,
    parameters: {
        layout: 'fullscreen',
    },
};

export default meta;
type Story = StoryObj<typeof PageSizeLoader>;

export const Default: Story = {
    args: {},
};
