import type { Meta, StoryObj } from '@storybook/react';
import { LoaderSpinner } from './LoaderSpinner';

const meta: Meta<typeof LoaderSpinner> = {
    title: 'Components/Loaders/LoaderSpinner',
    component: LoaderSpinner,
    parameters: {
        layout: 'centered',
    },
    tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof LoaderSpinner>;

export const Default: Story = {
    args: {},
};

// Moż<PERSON>z dodać więcej wariantów, jeśli będą potrzebne
export const InContainer: Story = {
    decorators: [
        Story => (
            <div style={{ padding: '20px', background: '#2d2d2d' }}>
                <Story />
            </div>
        ),
    ],
};
