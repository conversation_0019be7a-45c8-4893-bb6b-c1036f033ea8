'use client';

import { useConfigStore } from '@/store/configStore';
import { ReactNode, useEffect, useState } from 'react';
import { LoaderSpinnerSmall } from '../Loaders';
import { REQUIRED_CONFIG_KEYS } from '@/config/envConfig';
import { getServerRuntimeConfig } from '@/dynamic-env/config';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

interface Props {
    children: ReactNode;
}
export const SetupEnv = ({ children }: Props) => {
    const { env, setEnv } = useConfigStore();
    const [isLoading, setIsLoading] = useState(true);
    // Stable QueryClient instance for React Query
    const [queryClient] = useState(() => new QueryClient());

    useEffect(() => {
        const loadConfig = async () => {
            try {
                const serverConfig = await getServerRuntimeConfig();

                const mappedConfig = {
                    API_URL: serverConfig.API_URL || '',
                    DEPLOYMENT_TYPE: serverConfig.DEPLOYMENT_TYPE || '',
                    STRIPE_PUBLISHABLE_KEY: serverConfig.STRIPE_PUBLISHABLE_KEY || '',
                    STRIPE_PRICING_TABLE_ID: serverConfig.STRIPE_PRICING_TABLE_ID || '',
                };

                // Ustawienie konfiguracji w Zustand store
                if (Object.keys(env).length === 0 && Object.values(mappedConfig).some(value => value !== '')) {
                    try {
                        setEnv(mappedConfig);
                    } catch (error) {
                        console.error('Failed to set environment configuration:', error);
                    }
                }
            } catch (error) {
                console.error('Failed to load server runtime config:', error);
            } finally {
                setIsLoading(false);
            }
        };

        loadConfig();
    }, [env, setEnv]);

    const missingKeys = REQUIRED_CONFIG_KEYS.filter(key => !(key in env));

    if (missingKeys.length > 0 || Object.keys(env).length === 0 || isLoading) {
        return (
            <div className="flex flex-col items-center justify-center h-screen">
                <LoaderSpinnerSmall />
            </div>
        );
    }

    // Provide React Query to the app once env is ready
    return <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>;
};
