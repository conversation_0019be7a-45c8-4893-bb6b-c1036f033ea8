'use client';

import { useTranslations } from 'next-intl';
import Image from 'next/image';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';

export const Breadcrumb = () => {
    const t = useTranslations();
    const pathname = usePathname();
    const router = useRouter();

    const isUUID = (str: string) => {
        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
        return uuidRegex.test(str);
    };

    // Store the original path segments including UUIDs
    const originalPathSegments = pathname.split('/').filter(segment => segment);

    // Create a map to track UUIDs and their positions
    const uuidMap: { [index: number]: string } = {};
    originalPathSegments.forEach((segment, index) => {
        if (isUUID(segment)) {
            uuidMap[index] = segment;
        }
    });

    // Filter out UUIDs for display purposes
    let displayPathSegments = originalPathSegments.filter(segment => !isUUID(segment));

    if (!pathname.startsWith('/dashboard')) {
        displayPathSegments = ['start', ...displayPathSegments];
    }

    const generateText = (segment: string) => t(`breadcrumb.${segment}`);

    // Function to generate the correct href with UUIDs preserved
    const generateHref = (index: number): string => {
        const startOffset = displayPathSegments[0] === 'start' ? 1 : 0;
        const segmentsToInclude = displayPathSegments.slice(startOffset, index + 1);

        // Build the path with UUIDs in the correct positions
        const fullPath: string[] = [];
        let displaySegmentIndex = 0;
        let originalIndex = 0;

        while (displaySegmentIndex < segmentsToInclude.length) {
            // Add the visible segment
            fullPath.push(segmentsToInclude[displaySegmentIndex]);
            displaySegmentIndex++;
            originalIndex++;

            // Check if there's a UUID after this segment in the original path
            if (uuidMap[originalIndex]) {
                fullPath.push(uuidMap[originalIndex]);
                originalIndex++;
            }
        }

        return `/${fullPath.join('/')}`;
    };

    return (
        <div className="flex items-center text-main-600 text-xs gap-2 cursor-pointer">
            {displayPathSegments.map((segment, index) => {
                const href = generateHref(index);
                const isLast = index === displayPathSegments.length - 1;
                const content = isLast ? (
                    <span className="text-main-100 underline">{generateText(segment)}</span>
                ) : (
                    <Link id="components_Breadcrumb_Breadcrumb_link_u8in0i" href={href} className="hover:underline">
                        {generateText(segment)}
                    </Link>
                );

                const backArrow = !isLast && (
                    <div onClick={() => router.back()} className="cursor-pointer">
                        <Image src="/assets/BackArrow.svg" alt="back arrow" width={16} height={16} />
                    </div>
                );

                return (
                    <div key={href} className="flex items-center gap-2">
                        {content}
                        {backArrow}
                    </div>
                );
            })}
        </div>
    );
};
