import { Meta, StoryObj } from '@storybook/react';
import { Breadcrumb } from './Breadcrumb';
import { NextIntlClientProvider } from 'next-intl';

const messages = {
    home: 'Home',
    products: 'Products',
    electronics: 'Electronics',
};

const meta: Meta<typeof Breadcrumb> = {
    title: 'Components/Breadcrumb',
    component: Breadcrumb,
    tags: ['autodocs'],
    decorators: [
        Story => (
            <NextIntlClientProvider locale="en" messages={messages}>
                <div className="bg-main-700 p-8">
                    <Story />
                </div>
            </NextIntlClientProvider>
        ),
    ],
};

export default meta;
type Story = StoryObj<typeof Breadcrumb>;

export const Default: Story = {
    args: {
        children: 'Click me',
        pathSegments: ['home', 'products', 'electronics'],
        translations: messages,
        backArrowSrc: '/assets/BackArrow.svg',
    },
};
