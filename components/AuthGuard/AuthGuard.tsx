'use client';

import { useAuthStore } from '@/store/authStore';
import { usePathname, useRouter } from 'next/navigation';
import { ReactNode, useEffect, useState } from 'react';
import { PageSizeLoader } from '../Loaders';
import { matchPath } from '@/common/matchPath';
import { useUserStore } from '@/store/userStore';

const PUBLIC_ROUTES = ['/', '/auth/*', '/termsandconditions'];
const PRIVATE_ROUTES = ['/dashboard/*'];

interface Props {
    children: ReactNode;
}

export const AuthGuard = ({ children }: Props) => {
    const pathname = usePathname();
    const router = useRouter();
    const { getIsAuthenticated, token } = useAuthStore();
    const { clearUser } = useUserStore();
    const [isLoading, setIsLoading] = useState(true);
    const [isAuthorized, setIsAuthorized] = useState(false);

    useEffect(() => {
        const auth = getIsAuthenticated();
        const isPublicRoute = PUBLIC_ROUTES.some(route => matchPath(pathname, route));
        const isPrivateRoute = PRIVATE_ROUTES.some(route => matchPath(pathname, route));

        const shouldAuthorize = (auth && !isPublicRoute) || (!auth && !isPrivateRoute);

        setIsAuthorized(shouldAuthorize);

        if (!auth && isPrivateRoute) {
            clearUser();
            router.replace('/');
        } else if (auth && isPublicRoute) {
            router.replace('/dashboard');
        }

        setIsLoading(false);
    }, [pathname, getIsAuthenticated, clearUser, router, token]);

    if (isLoading || !isAuthorized) {
        return <PageSizeLoader />;
    }

    return <>{children}</>;
};
