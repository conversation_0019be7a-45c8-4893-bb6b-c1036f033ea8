import Link from 'next/link';
import { LinkProps } from 'next/link';

interface Props extends LinkProps, Omit<React.AnchorHTMLAttributes<HTMLAnchorElement>, 'href'> {
    children?: React.ReactNode;
}

export const LinkGradientSmall = ({ children, href, ...props }: Props) => {
    return (
        <Link id="components_Links_LinkGradientSmall_link_z90jbd"
            href={href}
            className="item-gradient disabled:opacity-50 disabled:cursor-not-allowed w-full text-sm text-center whitespace-nowrap py-1 text-white px-6 rounded-xl"
            {...props}
        >
            {children ?? 'Click'}
        </Link>
    );
};
