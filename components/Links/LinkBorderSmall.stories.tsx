import type { Meta, StoryObj } from '@storybook/react';
import { LinkBorderSmall } from './LinkBorderSmall';

const meta: Meta<typeof LinkBorderSmall> = {
    title: 'Components/Links/LinkBorderSmall',
    component: LinkBorderSmall,
    parameters: {
        layout: 'centered',
    },
    tags: ['autodocs'],
    decorators: [
        Story => (
            <div className="bg-main-400 p-4">
                <Story />
            </div>
        ),
    ],
};

export default meta;
type Story = StoryObj<typeof LinkBorderSmall>;

export const Default: Story = {
    args: {
        children: 'Click me',
        href: '#',
    },
};

export const WithCustomText: Story = {
    args: {
        children: 'Custom Text',
        href: '#',
    },
};

export const WithOnClick: Story = {
    args: {
        children: 'Click Event',
        href: '#',
        onClick: () => alert('Button clicked!'),
    },
};
