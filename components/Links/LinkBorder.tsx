import Link from 'next/link';
import { LinkProps } from 'next/link';

interface Props extends LinkProps, Omit<React.AnchorHTMLAttributes<HTMLAnchorElement>, 'href'> {
    children?: React.ReactNode;
}

export const LinkBorder = ({ children, href, ...props }: Props) => {
    return (
        <Link id="components_Links_LinkBorder_link_5dlq58"
            href={href}
            className="border-gradient disabled:opacity-50 whitespace-nowrap disabled:cursor-not-allowed text-main-600 p-3 overflow-hidden w-full text-sm text-center rounded-md"
            {...props}
        >
            {children ?? 'Click'}
        </Link>
    );
};
