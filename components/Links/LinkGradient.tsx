import Link from 'next/link';
import { LinkProps } from 'next/link';

interface Props extends LinkProps, Omit<React.AnchorHTMLAttributes<HTMLAnchorElement>, 'href'> {
    children?: React.ReactNode;
}

export const LinkGradient = ({ children, href, ...props }: Props) => {
    return (
        <Link id="components_Links_LinkGradient_link_7bzjp7"
            href={href}
            className="item-gradient text-sm disabled:opacity-50 w-full whitespace-nowrap disabled:cursor-not-allowed text-white p-3 rounded-md text-center"
            {...props}
        >
            {children ?? 'Click'}
        </Link>
    );
};
