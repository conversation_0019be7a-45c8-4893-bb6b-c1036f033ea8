import type { <PERSON>a, StoryObj } from '@storybook/react';
import { LinkBorder } from './LinkBorder';

const meta: Meta<typeof LinkBorder> = {
    title: 'Components/Links/LinkBorder',
    component: LinkBorder,
    tags: ['autodocs'],
    decorators: [
        Story => (
            <div className=" bg-main-700 p-8">
                <Story />
            </div>
        ),
    ],
};

export default meta;
type Story = StoryObj<typeof LinkBorder>;

export const Default: Story = {
    args: {
        href: '#',
        children: 'Przykładowy link',
    },
};

export const WithoutChildren: Story = {
    args: {
        href: '#',
    },
};

export const LongText: Story = {
    args: {
        href: '#',
        children: 'To jest bardzo długi tekst linku, który może się zawijać',
    },
};

export const ExternalLink: Story = {
    args: {
        href: 'https://example.com',
        children: '<PERSON>',
    },
};
