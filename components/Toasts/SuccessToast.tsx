import Image from 'next/image';

interface Props {
    title: string;
    message: string;
}

export const SuccessToast = ({ title, message }: Props) => {
    return (
        <div className="flex flex-row gap-4 pr-8 w-full">
            <Image src="/icons/check_circle.svg" alt="Toast Icon" width={24} height={24} />
            <div className="flex flex-col gap-1 text-main-600 w-full">
                <p className="text-lg font-bold">{title}</p>
                <p>{message}</p>
            </div>
        </div>
    );
};
