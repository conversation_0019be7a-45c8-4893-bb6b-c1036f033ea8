import { ErrorClassFields } from '@/types/errorClass';
import { useTranslations } from 'next-intl';
import Image from 'next/image';

export const ErrorToast = ({ type, code, message }: ErrorClassFields) => {
    const t = useTranslations('error_modal_status');

    const messageElement = message ? <p>{message}</p> : null;

    return (
        <div className="flex flex-row gap-4 pr-12 w-full">
            <Image src="/icons/check_cancel.svg" alt="Toast Icon" width={24} height={24} />
            <div className="flex flex-col gap-1 text-main-600 w-full">
                <p className="text-lg font-bold">{t('title')}</p>
                <div className="flex text-sm text-main-800 flex-col gap-0 leading-5">
                    <p className="flex flex-row gap-2">
                        {t(type.toLowerCase())}
                        {messageElement}
                    </p>
                    <p>
                        {t('code')}: {code}
                    </p>
                </div>
            </div>
        </div>
    );
};
