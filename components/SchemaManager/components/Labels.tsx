export const SmallLabelPrimary = ({ label }: { label: string }) => {
    return <span className="text-xs text-main-100">{label}</span>;
};

export const TextWithSmallLabel = ({ label, text }: { label: string; text: string }) => {
    return (
        <div className="flex flex-row items-baseline gap-1">
            <span className="text-xs text-main-600/50">{label}</span>
            <span className="text-sm text-main-600">{text}</span>
        </div>
    );
};
