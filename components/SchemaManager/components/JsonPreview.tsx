import { transformSchemaResponseToSchema } from '@/common/schemaBuilderTools';
import { IssuerSchema } from '@/types/issuerSchema';
import ReactJson from 'react-json-view';

interface Props {
    data: IssuerSchema | undefined;
}
export const JsonPreview = ({ data }: Props) => {
    if (!data) return null;
    return (
        <ReactJson
            src={transformSchemaResponseToSchema(data)}
            theme="twilight"
            style={{ background: 'transparent' }}
            displayDataTypes={false}
            collapsed={2}
            name={false}
            enableClipboard={true}
        />
    );
};
