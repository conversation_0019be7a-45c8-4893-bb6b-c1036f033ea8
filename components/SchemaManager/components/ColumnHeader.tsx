import { Hint } from '@/components/Hint';

interface Props {
    title: string;
    hintId?: string;
    children?: React.ReactNode;
}
export const ColumnHeader = ({ title, children, hintId }: Props) => {
    return (
        <div className="flex justify-between items-center border-b h-10 border-main-800/20 pb-4">
            <div className="flex gap-x-3">
                <h2 className="text-lg font-semibold text-main-600">{title}</h2>
                {hintId && <Hint id={hintId} />}
            </div>
            {children && <div>{children}</div>}
        </div>
    );
};
