import { motion } from 'motion/react';

interface Props {
    isSaving: boolean;
    lastSaved?: Date | null;
}

export const DraftSavingIndicator = ({ isSaving }: Props) => {
    if (isSaving) {
        return (
            <div className="flex items-center gap-2 text-white">
                {/* Obracające się kółeczko */}
                <motion.div
                    className="w-4 h-4 border-2 border-t-transparent border-white rounded-full"
                    animate={{ rotate: 360 }}
                    transition={{ repeat: Infinity, duration: 1, ease: 'linear' }}
                />

                {/* Migający napis */}
                <motion.span
                    animate={{ opacity: [1, 0.4, 1] }}
                    transition={{ repeat: Infinity, duration: 1.5 }}
                    className="text-sm font-medium whitespace-nowrap"
                >
                    Saving...
                </motion.span>
            </div>
        );
    }

    // if (showSavedMessage && lastSaved) {
    //     return (
    //         <motion.div
    //             initial={{ opacity: 0, scale: 0.8 }}
    //             animate={{ opacity: 1, scale: 1 }}
    //             exit={{ opacity: 0, scale: 0.8 }}
    //             className="flex items-center gap-2 text-green-400"
    //         >
    //             {/* Checkmark icon */}
    //             <div className="w-4 h-4 rounded-full bg-green-400 flex items-center justify-center">
    //                 <svg className="w-2.5 h-2.5 text-white" fill="currentColor" viewBox="0 0 20 20">
    //                     <path
    //                         fillRule="evenodd"
    //                         d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
    //                         clipRule="evenodd"
    //                     />
    //                 </svg>
    //             </div>

    //             <span className="text-sm font-medium whitespace-nowrap">Draft saved</span>
    //         </motion.div>
    //     );
    // }

    return null;
};
