'use client';

import { useState, useEffect, useRef } from 'react';
import { Editor, DiffEditor, type Monaco } from '@monaco-editor/react';
import OneDarkPro from '@/theme/marco.custom.json';
import { SchemaVersion } from '@/types/schemaManager';

interface Props {
    json: string;
    diffJson?: string;
    onSave: (parsed: SchemaVersion) => void;
}

export const JsonEditor = ({ json, diffJson, onSave }: Props) => {
    const [jsonValidationError, setJsonValidationError] = useState<string | null>(null);
    const [internalJson, setInternalJson] = useState<string>('');
    const isInternalUpdate = useRef(false);

    const formatJsonIfNeeded = (jsonString: string) => {
        try {
            const parsed = JSON.parse(jsonString);
            return JSON.stringify(parsed, null, 2);
        } catch {
            return jsonString;
        }
    };

    useEffect(() => {
        if (!isInternalUpdate.current) {
            const formatted = formatJsonIfNeeded(json);
            setInternalJson(formatted);
        }
        isInternalUpdate.current = false;
    }, [json]);

    const handleJsonChange = (value: string | undefined) => {
        const jsonValue = value || '';
        setInternalJson(jsonValue);
        setJsonValidationError(null);
        isInternalUpdate.current = true;

        if (jsonValue.trim()) {
            try {
                const parsed = JSON.parse(jsonValue);
                if (!parsed.$metadata || !parsed.title || !parsed.properties?.credentialSubject) {
                    setJsonValidationError(
                        'Invalid schema structure. Missing required fields ($metadata, title, properties.credentialSubject)'
                    );
                    return;
                }
                onSave(parsed);
            } catch {
                setJsonValidationError('Invalid JSON format');
            }
        }
    };

    const handleEditorDidMount = (monaco: Monaco) => {
        const rules = OneDarkPro.tokenColors
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            .flatMap((tokenColor: any) => {
                const scopes = Array.isArray(tokenColor.scope) ? tokenColor.scope : [tokenColor.scope].filter(Boolean);
                return scopes.map((scope: string) => ({
                    token: scope || '',
                    foreground: tokenColor.settings?.foreground?.replace('#', '') || undefined,
                    background: tokenColor.settings?.background?.replace('#', '') || undefined,
                    fontStyle: tokenColor.settings?.fontStyle || undefined,
                }));
            })
            .filter(rule => rule.token);

        const monacoTheme = {
            base: 'vs-dark' as const,
            inherit: true,
            rules,
            colors: OneDarkPro.colors,
        };

        monaco.editor.defineTheme('OneDarkPro', monacoTheme);
    };

    return (
        <div className="flex flex-col h-full min-h-96 gap-2">
            {diffJson ? (
                <DiffEditor
                    original={formatJsonIfNeeded(diffJson)}
                    modified={internalJson}
                    theme="OneDarkPro"
                    beforeMount={handleEditorDidMount}
                    className="bg-transparent h-full"
                    options={{
                        minimap: { enabled: false },
                        renderSideBySide: true, // false -> inline diff
                        readOnly: false,
                        fontSize: 13,
                        automaticLayout: true,
                        wordWrap: 'on',
                    }}
                />
            ) : (
                <Editor
                    defaultLanguage="json"
                    value={internalJson}
                    onChange={handleJsonChange}
                    theme="OneDarkPro"
                    beforeMount={handleEditorDidMount}
                    className="bg-transparent h-full rounded-lg overflow-hidden"
                    options={{
                        minimap: { enabled: false },
                        scrollBeyondLastLine: false,
                        fontSize: 13,
                        lineNumbers: 'on',
                        automaticLayout: true,
                        wordWrap: 'on',
                        bracketPairColorization: { enabled: true },
                        folding: true,
                        renderWhitespace: 'selection',
                        tabSize: 2,
                        insertSpaces: true,
                    }}
                />
            )}

            {jsonValidationError && (
                <div className="text-red-400 text-sm bg-red-900/20 border border-red-800/30 rounded p-3">
                    {jsonValidationError}
                </div>
            )}
        </div>
    );
};
