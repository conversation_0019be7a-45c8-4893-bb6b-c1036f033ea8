import { SchemaVersion } from '@/types/schemaManager';
import Image from 'next/image';
import { TextWithSmallLabel } from './Labels';
import { ButtonBorder, ButtonGradient, ButtonGradientSmall } from '@/components/Buttons';
import { ButtonBorderIcon } from '@/components/Buttons/ButtonBorderIcon';
import { ColumnsKeys } from '../SchemaBuilder';
import { DraftSavingIndicator } from './SaveLoad';
import { useMemo, useState } from 'react';
import { Hint } from '@/components/Hint';
import { TextInput } from '@/components/FormFields';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { createSchemaMetadataSchema, SchemaMetadataForm } from '@/validation/schemaMetadataValidation';
import { useTranslations } from 'next-intl';
import { FormValidationError } from '@/components/FormValidationError';

interface Props {
    schemaVersion: SchemaVersion;
    activeColumns: Array<string>;
    isDraftSaving: boolean;
    lastAutoSaved?: Date | null;
    onBackButtonClick: () => void;
    onColumnClick: (columnKey: string) => void;
    onPublish: () => void;
    onUpdateSchemaMetadata: (data: { name: string; description: string }) => void;
}

export const SchemaBuilderNavbar = ({
    schemaVersion,
    activeColumns,
    isDraftSaving,
    lastAutoSaved,
    onBackButtonClick,
    onColumnClick,
    onPublish,
    onUpdateSchemaMetadata,
}: Props) => {
    const [showMetadataEditForm, setShowMetadataEditForm] = useState(false);
    const t = useTranslations();

    // Build schema & RHF instance
    const schema = useMemo(() => createSchemaMetadataSchema(t), [t]);
    const {
        register,
        handleSubmit,
        reset,
        formState: { errors, isValid },
    } = useForm<SchemaMetadataForm>({
        resolver: zodResolver(schema),
        defaultValues: {
            name: schemaVersion.name,
            description: schemaVersion.description,
        },
        mode: 'all',
        reValidateMode: 'onChange',
    });

    // Keep form defaults in sync when user opens panel later
    const handleOpenEdit = () => {
        reset({ name: schemaVersion.name, description: schemaVersion.description }, { keepDefaultValues: false });
        setShowMetadataEditForm(prev => !prev);
    };

    const onProceed = handleSubmit(data => {
        onUpdateSchemaMetadata(data);
        setShowMetadataEditForm(false);
    });

    const onCancel = () => {
        reset({ name: schemaVersion.name, description: schemaVersion.description });
        setShowMetadataEditForm(false);
    };

    const actions = [
        {
            label: 'Attributes',
            key: ColumnsKeys.ATTRIBUTES,
            image: '/icons/attributes_bar.svg',
        },
        {
            label: 'JSON',
            key: ColumnsKeys.JSON_PREVIEW,
            image: '/icons/json_bar.svg',
        },
        {
            label: 'Ai',
            key: ColumnsKeys.AI,
            image: '/icons/ai_bar.svg',
        },
        {
            label: 'History',
            key: ColumnsKeys.HISTORY,
            image: '/icons/history_bar.svg',
        },
    ];

    const renderBack = () => {
        return (
            <div className="flex flex-row gap-2 text-main-100 items-center cursor-pointer" onClick={onBackButtonClick}>
                <Image src="/assets/back_arrow_blue.svg" alt="back arrow" width={8} height={8} />
                <span className="text-sm">Back to Schema List</span>
            </div>
        );
    };

    const renderVersionDetails = () => {
        return (
            <div
                onClick={handleOpenEdit}
                className="flex flex-row cursor-pointer select-none justify-between items-center gap-4 border-main-1100 bg-main-600/5 rounded-full border px-4"
            >
                <TextWithSmallLabel label="name:" text={schemaVersion.name} />
                <div className="w-0.5 h-full border-l border-main-1100" />
                <TextWithSmallLabel label="type:" text={schemaVersion.type} />
                <div className="w-0.5 h-full border-l border-main-1100" />
                <TextWithSmallLabel label="v." text={schemaVersion.version.toString()} />
            </div>
        );
    };

    const renderSchemaMetadataEditForm = () => {
        if (!showMetadataEditForm) return;
        return (
            <div className="flex flex-col justify-center gap-4 items-start bg-main-600/5 rounded-lg p-4">
                <div className="flex space-x-3">
                    <h1>Schema Metadata</h1>
                    <Hint id="issuer_details_schema_schema_metadata" />
                </div>

                <form className="flex flex-row gap-4 justify-between w-full" onSubmit={onProceed}>
                    <div className="flex-1">
                        <TextInput label="Name" {...register('name')} onClick={e => e.stopPropagation()} />
                        <FormValidationError message={errors.name?.message} />
                    </div>
                    <div className="flex-1">
                        <TextInput
                            label="Description"
                            {...register('description')}
                            onClick={e => e.stopPropagation()}
                        />
                        <FormValidationError message={errors.description?.message} />
                    </div>
                    <div className="flex-1">
                        <TextInput
                            disabled
                            label="Type"
                            value={schemaVersion.type}
                            onClick={e => e.stopPropagation()}
                        />
                    </div>
                    <div className="flex flex-row gap-4 flex-1 items-center pt-6">
                        <ButtonGradient
                            id="components_SchemaBuilderNavbar_button_il6gil"
                            type="submit"
                            disabled={!isValid}
                        >
                            Proceed
                        </ButtonGradient>
                        <ButtonBorder
                            id="components_SchemaBuilderNavbar_button_er8jqt"
                            type="button"
                            onClick={onCancel}
                        >
                            Cancel
                        </ButtonBorder>
                    </div>
                </form>
            </div>
        );
    };

    const renderActions = () => {
        return (
            <div className="flex flex-row justify-between gap-4 relative">
                <div className="absolute -left-24 top-1/2 -translate-y-1/2">
                    <DraftSavingIndicator isSaving={isDraftSaving} lastSaved={lastAutoSaved} />
                </div>
                {actions.map(action => (
                    <ButtonBorderIcon
                        id="components_SchemaBuilderNavbar_button_kh57qa"
                        type="button"
                        active={activeColumns.includes(action.key)}
                        key={action.key}
                        onClick={() => onColumnClick?.(action.key)}
                    >
                        <Image src={action.image} alt={`${action.label}`} width={18} height={18} />
                    </ButtonBorderIcon>
                ))}
                <ButtonGradientSmall id="components_SchemaBuilderNavbar_button_4rwaos" onClick={onPublish}>
                    Publish
                </ButtonGradientSmall>
            </div>
        );
    };

    return (
        <div className="flex flex-col gap-2">
            <div className="flex flex-row justify-between py-2">
                {renderBack()}
                {renderVersionDetails()}
                {renderActions()}
            </div>
            {renderSchemaMetadataEditForm()}
        </div>
    );
};

export const SchemaOfferingNavbar = ({ onBackButtonClick }: Pick<Props, 'onBackButtonClick'>) => {
    const renderBack = () => {
        return (
            <div className="flex flex-row gap-2 text-main-100 items-center cursor-pointer" onClick={onBackButtonClick}>
                <Image src="/assets/back_arrow_blue.svg" alt="back arrow" width={8} height={8} />
                <span className="text-sm">Back to Schema List</span>
            </div>
        );
    };

    return (
        <div className="flex flex-col gap-2">
            <div className="flex flex-row justify-between py-2">{renderBack()}</div>
        </div>
    );
};
