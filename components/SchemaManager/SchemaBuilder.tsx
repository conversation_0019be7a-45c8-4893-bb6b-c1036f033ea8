'use client';

import { DATA_TYPE_OPTIONS } from '@/const/SchemaPropertyType';
import { useSchemaManager } from '@/hooks/useSchemaManager';
import { SchemaAttribute } from '@/types/schemaManager';
import { useTranslations } from 'next-intl';
import { TextInput } from '@/components/FormFields';
import { CheckboxWithoutRHF } from '@/components/Checkbox/Checkbox';
import { SelectInputWithoutRHF } from '@/components/FormFields/SelectInput';
import { ButtonBorderBasicSmall } from '@/components/Buttons/ButtonBorderBasicSmall';
import Image from 'next/image';
import { SchemaBuilderNavbar } from './components/SchemaBuilderNavbar';
import { Panel, PanelGroup, PanelResizeHandle } from 'react-resizable-panels';
import { Fragment, useState } from 'react';
import { ColumnHeader } from './components/ColumnHeader';
import { JsonEditor } from './components/JsonEditor';
import { EmptyList } from './components/EmptyList';
import { SchemaVersionHistoryListItem } from './components/SchemaListItem';

export const ColumnsKeys = {
    ATTRIBUTES: 'attributes',
    JSON_PREVIEW: 'json_preview',
    AI: 'ai',
    HISTORY: 'history',
};

export const SchemaBuilder = () => {
    const t = useTranslations('schema_builder');
    const {
        schemaVersion,
        editingAttribute,
        diffJson,
        setEditingAttribute,
        addAttribute,
        addNestedAttribute,
        removeAttribute,
        updateAttribute,
        handleBackToSchemaList,
        generateJsonSchema,
        handlePublishSchema,
        updateSchemaBasicFields,
        handleHistoryDiffPreview,
        handleLoadHistorySnapshot,

        importSchemaFromJson,
        isUpdatingFromJson,
        allDraftsOfSchemaType,
        isAutoSaving,
        lastAutoSaved,
    } = useSchemaManager();

    const [activeColumns, setActiveColumns] = useState<Array<string>>([
        ColumnsKeys.ATTRIBUTES,
        ColumnsKeys.JSON_PREVIEW,
    ]);

    const renderEditableAttributeForm = (attr: SchemaAttribute) => {
        return (
            <div className="space-y-3">
                <div className="grid grid-cols-2 gap-2">
                    <TextInput
                        label="Name"
                        type="text"
                        value={attr.name}
                        onChange={e =>
                            updateAttribute(attr.id, {
                                name: e.target.value,
                            })
                        }
                        onClick={e => e.stopPropagation()}
                    />
                    <TextInput
                        label="Title"
                        type="text"
                        value={attr.title}
                        onChange={e =>
                            updateAttribute(attr.id, {
                                title: e.target.value,
                            })
                        }
                        onClick={e => e.stopPropagation()}
                    />
                </div>

                <div className="grid grid-cols-2 gap-2">
                    <SelectInputWithoutRHF
                        name="Type"
                        label="Type"
                        value={attr.dataType}
                        onChange={val =>
                            updateAttribute(attr.id, {
                                dataType: val as string,
                            })
                        }
                        onClick={e => e.stopPropagation()}
                        options={DATA_TYPE_OPTIONS}
                    />

                    <TextInput
                        label="Description"
                        value={attr.description || ''}
                        onChange={e =>
                            updateAttribute(attr.id, {
                                description: e.target.value,
                            })
                        }
                        onClick={e => e.stopPropagation()}
                    />
                </div>

                <div className="flex items-center justify-between gap-2" onClick={e => e.stopPropagation()}>
                    <div>
                        <CheckboxWithoutRHF
                            mainLabel={t('required_label')}
                            name="Required"
                            checked={attr.required || false}
                            onChange={e =>
                                updateAttribute(attr.id, {
                                    required: e.target.checked,
                                })
                            }
                            className="w-4 h-4"
                        />
                    </div>
                    <Image
                        src="/icons/trash.svg"
                        alt="remove attribute"
                        className="cursor-pointer"
                        onClick={() => removeAttribute(attr.id)}
                        width={20}
                        height={20}
                    />
                </div>
            </div>
        );
    };

    // Helper function to flatten nested attributes into a single array with depth information
    const flattenAttributes = (
        attributes: SchemaAttribute[],
        depth: number = 0,
        parentId?: string
    ): Array<{ attr: SchemaAttribute; depth: number; parentId?: string }> => {
        const result: Array<{ attr: SchemaAttribute; depth: number; parentId?: string }> = [];

        attributes.forEach(attr => {
            result.push({ attr, depth, parentId });

            if (attr.dataType === 'object' && attr.properties && attr.properties.length > 0) {
                result.push(...flattenAttributes(attr.properties, depth + 1, attr.id));
            }
        });

        return result;
    };

    const renderEditableAttribute = (attr: SchemaAttribute) => {
        return (
            <div className="space-y-3 py-2">
                {renderEditableAttributeForm(attr)}

                {/* Show add nested button if this is an object type */}
                {attr.dataType === 'object' && (
                    <>
                        <div className="border-t border-main-800/30 pt-3 mt-3">
                            <div className="flex justify-end items-end">
                                <div>
                                    <ButtonBorderBasicSmall id="components_SchemaManager_SchemaBuilder_button_o1knab"
                                        onClick={e => {
                                            e.stopPropagation();
                                            addNestedAttribute(attr.id);
                                        }}
                                        type="button"
                                    >
                                        {t('add_nested_button')}
                                    </ButtonBorderBasicSmall>
                                </div>
                            </div>
                        </div>
                        <EmptyList show={!attr.properties || attr.properties.length === 0} />
                    </>
                )}
            </div>
        );
    };

    const renderAttribute = (attr: SchemaAttribute) => {
        return (
            <div className="space-y-3">
                <div
                    className="cursor-pointer"
                    onClick={e => {
                        e.stopPropagation();
                        setEditingAttribute(attr.id);
                    }}
                >
                    <div className="text-xs text-main-800 gap-1 h-8 items-center flex justify-between flex-row">
                        <div className="flex flex-row gap-1 items-baseline">
                            <span className="text-main-600 text-sm">{attr.name}</span>
                            <span>({attr.dataType})</span>
                            {attr.required && <span className="text-red-400 ml-1">*</span>}
                        </div>
                        {attr.dataType === 'object' && (
                            <div className="flex justify-end items-center text-main-600">
                                <span
                                    onClick={e => {
                                        e.stopPropagation();
                                        addNestedAttribute(attr.id);
                                    }}
                                    className="text-main-100 underline text-xs cursor-pointer"
                                >
                                    {t('add_nested_button')}
                                </span>
                            </div>
                        )}
                    </div>
                </div>
            </div>
        );
    };

    const renderColumnAttributes = () => {
        if (!activeColumns.includes(ColumnsKeys.ATTRIBUTES)) return null;

        // Get flattened list of all attributes with their depth information
        const flattenedAttributes = flattenAttributes(schemaVersion.attributes);

        return (
            <Panel minSize={30} defaultSize={30} maxSize={60}>
                <div className="p-4 bg-main-600/5 rounded-xl flex flex-col h-full">
                    <ColumnHeader title="Attributes" hintId="issuer_details_schema_builder_attributes">
                        <ButtonBorderBasicSmall id="components_SchemaManager_SchemaBuilder_button_jm00ho" onClick={addAttribute}>Add attribute</ButtonBorderBasicSmall>
                    </ColumnHeader>

                    <div className="flex flex-col flex-1 gap-4 pt-4 overflow-y-auto bg-transparent without-scrollbar min-h-0">
                        {flattenedAttributes.map(({ attr, depth }) => (
                            <div key={attr.id} className="flex items-start gap-2">
                                {/* Render indentation and single arrow for nested items */}
                                {depth > 0 && (
                                    <div
                                        className="flex items-center pl-2 pt-1"
                                        style={{ marginLeft: `${(depth - 1) * 32}px` }}
                                    >
                                        <Image
                                            src="/icons/nested_arrow.svg"
                                            alt="nested"
                                            width={16}
                                            height={16}
                                            className="text-main-600"
                                        />
                                    </div>
                                )}

                                <div className="flex-1 border border-main-1100 rounded-xl p-1 px-3">
                                    {editingAttribute === attr.id
                                        ? renderEditableAttribute(attr)
                                        : renderAttribute(attr)}
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </Panel>
        );
    };

    const renderColumnJson = () => {
        if (!activeColumns.includes(ColumnsKeys.JSON_PREVIEW)) return null;
        return (
            <Panel>
                <div className="p-4 flex flex-col gap-4 h-full bg-main-600/5 rounded-xl">
                    <ColumnHeader title={t('json_editor')} hintId="issuer_details_schema_builder_json" />

                    <JsonEditor
                        json={JSON.stringify(generateJsonSchema(schemaVersion))}
                        diffJson={diffJson && JSON.stringify(generateJsonSchema(diffJson.schemaBody))}
                        onSave={parsedJson => {
                            // Prevent circular updates when we're already updating from JSON
                            if (isUpdatingFromJson) {
                                return;
                            }

                            try {
                                const result = importSchemaFromJson(JSON.stringify(parsedJson));
                                if (!result.success && result.error) {
                                    console.error('Schema import error:', result.error);
                                    // Możesz tutaj dodać toast z błędem jeśli chcesz
                                }
                            } catch (error) {
                                console.error('Unexpected error during schema import:', error);
                            }
                        }}
                    />
                </div>
            </Panel>
        );
    };
    const renderColumnAi = () => {
        if (!activeColumns.includes(ColumnsKeys.AI)) return null;
        return (
            <Panel>
                <div className="p-4 flex flex-col gap-4 bg-main-600/5 h-full rounded-xl">
                    <ColumnHeader title="AI" hintId="issuer_details_schema_builder_ai" />
                    <div className="flex justify-center items-center w-full flex-1 overflow-y-auto min-h-0">
                        <div className="relative w-48 h-48">
                            <Image src="/assets/mr_block.png" alt="AI" objectFit="contain" fill />
                        </div>
                    </div>
                </div>
            </Panel>
        );
    };
    const renderColumnHistory = () => {
        if (!activeColumns.includes(ColumnsKeys.HISTORY)) return null;
        return (
            <Panel minSize={32} defaultSize={30} maxSize={60}>
                <div className="p-4 flex flex-col gap-4 bg-main-600/5 h-full rounded-xl">
                    <ColumnHeader title="History" hintId="issuer_details_schema_builder_history" />
                    <div className="flex flex-col gap-2 overflow-y-auto without-scrollbar min-h-0">
                        {allDraftsOfSchemaType.map(schema => (
                            <SchemaVersionHistoryListItem
                                key={schema.id}
                                data={schema}
                                onRestoreVersion={handleLoadHistorySnapshot}
                                active={diffJson?.id === schema.id}
                                onClick={() => handleHistoryDiffPreview(schema.id)}
                            />
                        ))}
                    </div>
                </div>
            </Panel>
        );
    };

    // Helper function to get active column keys in order
    const getActiveColumnKeys = () => {
        const allColumns = [ColumnsKeys.ATTRIBUTES, ColumnsKeys.JSON_PREVIEW, ColumnsKeys.AI, ColumnsKeys.HISTORY];
        return allColumns.filter(column => activeColumns.includes(column));
    };

    // Main function to render panels with conditional resize handles
    const renderPanelsWithHandles = () => {
        const activeColumnKeys = getActiveColumnKeys();
        const elements: React.ReactNode[] = [];

        activeColumnKeys.forEach((columnKey, index) => {
            // Add the panel
            switch (columnKey) {
                case ColumnsKeys.ATTRIBUTES:
                    elements.push(<Fragment key={`panel-${index}-${columnKey}`}>{renderColumnAttributes()}</Fragment>);
                    break;
                case ColumnsKeys.JSON_PREVIEW:
                    elements.push(<Fragment key={`panel-${index}-${columnKey}`}>{renderColumnJson()}</Fragment>);
                    break;
                case ColumnsKeys.AI:
                    elements.push(<Fragment key={`panel-${index}-${columnKey}`}>{renderColumnAi()}</Fragment>);
                    break;
                case ColumnsKeys.HISTORY:
                    elements.push(<Fragment key={`panel-${index}-${columnKey}`}>{renderColumnHistory()}</Fragment>);
                    break;
            }

            // Add resize handle if there's a next column
            if (index < activeColumnKeys.length - 1) {
                elements.push(
                    <PanelResizeHandle
                        className="w-6 flex flex-col gap-1 justify-center items-center"
                        key={`handle-${index}`}
                    >
                        <div className="h-full border border-main-1100  border-dashed w-[1px]" />
                        <Image src="/icons/drag_handle.svg" alt="Drag Handle" width={20} height={20} />
                        <div className="h-full border border-main-1100 border-dashed w-[1px]" />
                    </PanelResizeHandle>
                );
            }
        });

        return elements;
    };

    return (
        <div className="px-10 flex flex-col gap-4 h-[calc(100vh_-_200px)]">
            <SchemaBuilderNavbar
                isDraftSaving={isAutoSaving}
                lastAutoSaved={lastAutoSaved}
                schemaVersion={schemaVersion}
                activeColumns={activeColumns}
                onBackButtonClick={handleBackToSchemaList}
                onColumnClick={(columnKey: string) => {
                    setActiveColumns(prev => {
                        if (prev.includes(columnKey)) {
                            return prev.filter(c => c !== columnKey);
                        }
                        return [...prev, columnKey];
                    });
                }}
                onPublish={handlePublishSchema}
                onUpdateSchemaMetadata={(data: { name: string; description: string }) => {
                    // Update basic fields through hook
                    updateSchemaBasicFields({ name: data.name, description: data.description });
                }}
            />
            <div className="flex flex-row items-start justify-center flex-1 min-h-0">
                <PanelGroup
                    className="h-full"
                    autoSaveId={`schema-builder-${schemaVersion.type}`}
                    direction="horizontal"
                >
                    {renderPanelsWithHandles()}
                </PanelGroup>
            </div>
        </div>
    );
};
