'use client';

import { useState } from 'react';
import { IssuerSchema } from '@/types/issuerSchema';
import { motion } from 'motion/react';
import { ButtonBorder, ButtonBorderSmall } from '@/components/Buttons';
import { useDefineSchema } from '@/hooks/useDefineSchema';
import { useTranslations } from 'next-intl';
import { getSchemaVersionListWithLatestOneDraft, sortedSchemaVersions } from '@/common/schemaBuilderTools';
import { SchemaListItem, SchemaVersionListItem } from './components/SchemaListItem';
import { EmptyList } from './components/EmptyList';
import { JsonPreview } from './components/JsonPreview';
import { SchemaStatus } from '@/const/SchemaStatus';
import { Hint } from '../Hint';

export const SchemaList = () => {
    const t = useTranslations('schema_builder');
    const [activeDefineSchema, setActiveDefineSchema] = useState<boolean>(false);
    const {
        onSubmit,
        isValid,
        handleSubmit,
        fieldsToRender,
        schemas,
        activeVersionId,
        setActiveVersionId,
        handleAddNewVersion,
        handleContinueEditing,
        handleSchemaOffering,
    } = useDefineSchema({
        setActiveDefineSchema,
    });
    const handleShowDefineSchema = () => {
        setActiveVersionId(null);
        setActiveDefineSchema(prev => !prev);
    };

    const handleShowJsonPreviewSchemaVersion = (versionId: string) => {
        setActiveDefineSchema(false);
        if (!versionId || versionId !== activeVersionId) {
            setActiveVersionId(versionId);
        } else {
            setActiveVersionId(null);
        }
    };

    // Grupowanie schematów po typie
    const groupedSchemas = schemas.reduce(
        (acc, schema) => {
            if (!acc[schema.type]) {
                acc[schema.type] = [];
            }
            acc[schema.type].push(schema);
            return acc;
        },
        {} as Record<string, IssuerSchema[]>
    );

    const uniqueSchemaTypes = Object.keys(groupedSchemas);

    const renderDefineSchemaForm = () => {
        return (
            <div className="space-y-8">
                {fieldsToRender}
                <ButtonBorder
                    id="components_SchemaManager_SchemaList_button_uxty50"
                    onClick={handleSubmit(onSubmit, errors => console.error('define schema form: ', errors))}
                    disabled={!isValid}
                >
                    {t('define_schema_button')}
                </ButtonBorder>
            </div>
        );
    };

    const renderDefineSchema = () => {
        if (!activeDefineSchema) return null;
        return (
            <motion.div
                key="schema-define"
                initial={{ opacity: 0, x: 50, scale: 0.95 }}
                animate={{ opacity: 1, x: 0, scale: 1 }}
                exit={{ opacity: 0, x: 50, scale: 0.95 }}
                transition={{
                    duration: 0.3,
                    ease: 'easeInOut',
                }}
                className="w-full max-w-xl pt-11 flex-1 overflow-hidden"
            >
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.1, duration: 0.3 }}
                    className="flex flex-col gap-4"
                >
                    <div className="bg-main-600/5 p-4 flex gap-4 flex-col rounded-lg w-full">
                        <div className="flex flex-row justify-start space-x-3">
                            <h1 className="text-xl self-center">Define Schema</h1>

                            <Hint id="issuer_details_schema_list_define_schema" />
                        </div>
                        {renderDefineSchemaForm()}
                    </div>
                </motion.div>
            </motion.div>
        );
    };

    const renderSchemas = () => {
        return (
            <div className="w-full max-w-xl flex-1">
                <div className="flex flex-col gap-4">
                    <div className="flex flex-row justify-between">
                        <div className="flex space-x-3">
                            <h1 className="text-xl self-center">Schema List</h1>
                            <Hint id="issuer_details_schema_list_list" />
                        </div>
                        <div className="self-center">
                            <ButtonBorderSmall
                                id="components_SchemaManager_SchemaList_button_hax3jx"
                                onClick={handleShowDefineSchema}
                            >
                                Add New Schema
                            </ButtonBorderSmall>
                        </div>
                    </div>
                    <div className="flex flex-col gap-4">
                        <EmptyList show={uniqueSchemaTypes.length === 0} />
                        {uniqueSchemaTypes.map(schemaType => {
                            const firstSchemaOfType = groupedSchemas[schemaType][0];
                            const versionsList = getSchemaVersionListWithLatestOneDraft(groupedSchemas[schemaType]);
                            const isActive = activeVersionId
                                ? versionsList.some(schema => schema.id === activeVersionId)
                                : false;

                            return (
                                <SchemaListItem
                                    key={schemaType}
                                    data={firstSchemaOfType}
                                    active={isActive}
                                    countOfCredentials={
                                        versionsList.map(v => v.issuedCount).reduce((a, b) => a + b, 0) || 0
                                    }
                                    onAddNewVersionClick={() => handleAddNewVersion()}
                                    onSchemaOfferingClick={() => {
                                        console.log('onSchemaOfferingClick', versionsList);
                                        // Use the latest PUBLISHED version's id (e.g., v1 PUBLISHED, v2 PUBLISHED, v3 DRAFT -> pick v2)
                                        const latestPublished = versionsList
                                            .filter(v => {
                                                if ('status' in v) {
                                                    return v.status === SchemaStatus.PUBLISHED;
                                                }
                                                return true; // brak pola status → przepuszczamy
                                            })
                                            .sort((a, b) => b.version - a.version)[0];
                                        console.log('🚀 ~ latestPublished:', latestPublished);

                                        if (latestPublished?.id) {
                                            handleSchemaOffering(latestPublished.id);
                                        } else {
                                            console.log('No published schema found');
                                        }
                                    }}
                                    newVersionAvailable={!versionsList.some(v => v.status === SchemaStatus.DRAFT)}
                                    schemaType={schemaType}
                                >
                                    <>
                                        {sortedSchemaVersions(versionsList).map(schema => (
                                            <SchemaVersionListItem
                                                key={schema.id}
                                                onClick={() => handleShowJsonPreviewSchemaVersion(schema.id)}
                                                active={activeVersionId === schema.id}
                                                data={schema}
                                            />
                                        ))}
                                    </>
                                </SchemaListItem>
                            );
                        })}
                    </div>
                </div>
            </div>
        );
    };

    const renderJsonPreviewSchema = () => {
        if (!activeVersionId) return null;

        const schema = schemas.find(s => s.id === activeVersionId);
        const schemaStatus = schema?.status;

        return (
            <motion.div
                key="schema-json-preview"
                initial={{ opacity: 0, x: 50, scale: 0.95 }}
                animate={{ opacity: 1, x: 0, scale: 1 }}
                exit={{ opacity: 0, x: 50, scale: 0.95 }}
                transition={{
                    duration: 0.3,
                    ease: 'easeInOut',
                }}
                className="w-full flex-1 pt-11 overflow-hidden"
            >
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.1, duration: 0.3 }}
                    className="flex flex-col gap-4 w-full"
                >
                    <div className="bg-main-600/5 p-4 rounded-lg gap-4 flex flex-col w-full">
                        <div className="flex flex-row justify-between">
                            <div className="flex space-x-3">
                                <h1 className="text-xl self-center">Json Preview</h1>
                                <Hint id="issuer_details_schema_list_json_preview" />
                            </div>
                            {schemaStatus === SchemaStatus.DRAFT && (
                                <div>
                                    <ButtonBorderSmall
                                        id="components_SchemaManager_SchemaList_button_teyyks"
                                        onClick={handleContinueEditing}
                                    >
                                        Continue Editing
                                    </ButtonBorderSmall>
                                </div>
                            )}
                        </div>
                        <JsonPreview data={schema} />
                    </div>
                </motion.div>
            </motion.div>
        );
    };

    return (
        <div className="px-10 py-4 h-full gap-4 flex justify-start w-full items-start">
            {renderSchemas()}
            {renderDefineSchema()}
            {renderJsonPreviewSchema()}
        </div>
    );
};
