'use client';
import { forwardRef } from 'react';
import clsx from 'clsx';
import type { Placement } from '@/contexts/HintContext';

type Props = {
    title?: string;
    description: string;
    placement: Exclude<Placement, 'auto'>;
    style: React.CSSProperties;
    onMouseDownCapture?: React.MouseEventHandler;
};

// Simple bubble rendered in a portal by parent
export const HintBubble = forwardRef<HTMLDivElement, Props>(function HintBubble(
    { title, description, placement, style, onMouseDownCapture },
    ref
) {
    const arrowBase = 'absolute w-0 h-0 border-6';
    const arrowByPlacement = {
        top: 'top-full left-4 border-l-transparent border-r-transparent border-b-transparent border-t-main-1300',
        bottom: 'bottom-full left-4 border-l-transparent border-r-transparent border-t-transparent border-b-main-1300',
        left: 'left-full top-1/2 -translate-y-1/2 border-t-transparent border-b-transparent border-r-transparent border-l-main-1300',
        right: 'right-full top-1/2 -translate-y-1/2 border-t-transparent border-b-transparent border-l-transparent border-r-main-1300',
    } as const;

    return (
        <div
            ref={ref}
            role="tooltip"
            onMouseDownCapture={onMouseDownCapture}
            style={style}
            className={clsx(
                'z-[1000] fixed px-3 py-2 flex flex-col gap-1 text-left text-sm text-main-600 bg-main-1300 rounded-lg shadow-lg w-72'
            )}
        >
            {title && <div className="font-semibold text-main-100">{title}</div>}
            <div className="text-main-600 leading-snug">{description}</div>
            <div className={clsx(arrowBase, arrowByPlacement[placement])} />
        </div>
    );
});
