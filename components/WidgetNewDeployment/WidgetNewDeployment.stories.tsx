import type { Meta, StoryObj } from '@storybook/react';
import { WidgetNewDeployment } from './WidgetNewDeployment';
import { messages } from '@/.storybook/i18n-mock';
import { NextIntlClientProvider } from 'next-intl';

const meta: Meta<typeof WidgetNewDeployment> = {
    title: 'Widgets/NewDeployment',
    component: WidgetNewDeployment,
    parameters: {
        layout: 'centered',
    },
    tags: ['autodocs'],
    decorators: [
        Story => (
            <NextIntlClientProvider messages={messages} locale="en">
                <div className="bg-main-400 text-white">
                    <Story />
                </div>
            </NextIntlClientProvider>
        ),
    ],
};

export default meta;
type Story = StoryObj<typeof WidgetNewDeployment>;

export const Default: Story = {
    args: {},
};
