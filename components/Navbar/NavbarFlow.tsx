import { RouterPaths } from '@/common/routerPaths';
import Image from 'next/image';
import Link from 'next/link';

interface Props {
    title: string;
}
export const NavbarFlow = ({ title }: Props) => {
    return (
        <div className="w-full h-20 flex flex-col gap-2 md:flex-row justify-between items-center px-10">
            <Link id="components_Navbar_NavbarFlow_link_b8iecg" href={RouterPaths.START_SCREEN} className="flex-1">
                <Image src={'/assets/NavLogo.svg'} alt="logo" width={200} height={100} />
            </Link>
            <div className="flex-1 flex justify-center">
                <Link id="components_Navbar_NavbarFlow_link_oirgav" href={RouterPaths.START_SCREEN} className="text-main-600 font-bold text-2xl">
                    {title}
                </Link>
            </div>
            <div className="flex-1" />
        </div>
    );
};
