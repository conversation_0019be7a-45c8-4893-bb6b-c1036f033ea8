import type { Meta, StoryObj } from '@storybook/react';
import { NavbarFlow } from './NavbarFlow';

const meta: Meta<typeof NavbarFlow> = {
    title: 'Components/NavbarFlow',
    component: NavbarFlow,
    parameters: {
        layout: 'fullscreen',
    },
    tags: ['autodocs'],
    decorators: [
        Story => (
            <div className="min-h-screen bg-main-700">
                <Story />
            </div>
        ),
    ],
};

export default meta;
type Story = StoryObj<typeof NavbarFlow>;

export const Default: Story = {
    args: {
        title: 'Example Title',
    },
};

export const LongTitle: Story = {
    args: {
        title: 'Very Long Example Title For Testing Purposes',
    },
};

export const ShortTitle: Story = {
    args: {
        title: 'Short',
    },
};
