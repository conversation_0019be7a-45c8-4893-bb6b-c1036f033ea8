import type { Meta, StoryObj } from '@storybook/react';
import { NavbarDashboard } from './NavbarDashboard';

const meta: Meta<typeof NavbarDashboard> = {
    title: 'Components/Navigation/NavbarDashboard',
    component: NavbarDashboard,
    parameters: {
        layout: 'fullscreen',
    },
    tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof NavbarDashboard>;

export const Default: Story = {
    args: {
        avatar: {
            icon: '/assets/Avatar.svg',
            username: '<EMAIL>',
        },
    },
};
