import { isJson } from '@/common/isJson';
import { useToast } from '@/hooks/useToast';
import { SuccessToast } from '../Toasts';
import { useTranslations } from 'next-intl';
import ReactJson from 'react-json-view';

// Function to parse and colorize log entries
const parseLogEntry = (log: string) => {
    // Regex to match: [timestamp] level [service] message
    const logRegex = /^(\[.*?\])\s+(\w+)\s+(\[.*?\])\s+(.*)$/;
    const match = log.match(logRegex);

    if (!match) {
        return { timestamp: '', level: '', service: '', message: log };
    }

    const [, timestamp, level, service, message] = match;
    return { timestamp, level: level.toLowerCase(), service, message };
};

// Function to get color class based on log level
const getLogLevelColor = (level: string) => {
    switch (level) {
        case 'error':
        case 'fatal':
            return 'text-red-400';
        case 'warn':
        case 'warning':
            return 'text-yellow-400';
        case 'info':
            return 'text-blue-400';
        case 'debug':
            return 'text-gray-400';
        case 'success':
            return 'text-green-400';
        default:
            return 'text-gray-300';
    }
};

// Function to extract and colorize JSON from text
const extractAndColorizeJson = (text: string): JSX.Element => {
    const parts = [];
    let currentIndex = 0;

    // Find all potential JSON start positions
    while (currentIndex < text.length) {
        const jsonStart = text.indexOf('{', currentIndex);

        if (jsonStart === -1) {
            // No more JSON found, add remaining text
            if (currentIndex < text.length) {
                parts.push({
                    type: 'text',
                    content: text.slice(currentIndex),
                });
            }
            break;
        }

        // Add text before JSON start
        if (jsonStart > currentIndex) {
            parts.push({
                type: 'text',
                content: text.slice(currentIndex, jsonStart),
            });
        }

        // Try to find the complete JSON object by counting braces
        let braceCount = 0;
        let jsonEnd = jsonStart;
        let inString = false;
        let escapeNext = false;

        for (let i = jsonStart; i < text.length; i++) {
            const char = text[i];

            if (escapeNext) {
                escapeNext = false;
                continue;
            }

            if (char === '\\') {
                escapeNext = true;
                continue;
            }

            if (char === '"' && !escapeNext) {
                inString = !inString;
                continue;
            }

            if (!inString) {
                if (char === '{') {
                    braceCount++;
                } else if (char === '}') {
                    braceCount--;
                    if (braceCount === 0) {
                        jsonEnd = i + 1;
                        break;
                    }
                }
            }
        }

        // Extract potential JSON
        const potentialJson = text.slice(jsonStart, jsonEnd);

        // Validate if it's actually valid JSON
        try {
            JSON.parse(potentialJson);
            // Add JSON part
            parts.push({
                type: 'json',
                content: potentialJson,
            });
            currentIndex = jsonEnd;
        } catch {
            // If it's not valid JSON, treat the opening brace as text and continue
            parts.push({
                type: 'text',
                content: text.slice(jsonStart, jsonStart + 1),
            });
            currentIndex = jsonStart + 1;
        }
    }

    // If no JSON was found, return the whole text as one text part
    if (parts.length === 0) {
        return <span className="text-gray-200">{text}</span>;
    }

    return (
        <span>
            {parts.map((part, index) => {
                if (part.type === 'json') {
                    return (
                        <span key={index} className="inline-block">
                            <ReactJson
                                src={JSON.parse(part.content)}
                                theme="twilight"
                                style={{ background: 'transparent' }}
                                displayDataTypes={false}
                                collapsed={8}
                                name={false}
                            />
                        </span>
                    );
                } else {
                    return (
                        <span key={index} className="text-gray-200">
                            {part.content}
                        </span>
                    );
                }
            })}
        </span>
    );
};

// Component to render a colorized log entry
export const LogEntry = ({ log }: { log: string }) => {
    const { timestamp, level, service, message } = parseLogEntry(log);
    const { showToast } = useToast();
    const t = useTranslations();

    if (!timestamp) {
        // If parsing failed, check if it's JSON
        if (isJson(log)) {
            return (
                <div className="font-mono text-sm">
                    <ReactJson
                        src={JSON.parse(log)}
                        theme="twilight"
                        style={{ background: 'transparent' }}
                        displayDataTypes={false}
                        collapsed={8}
                        name={false}
                    />
                </div>
            );
        }
        // Otherwise show the raw log
        return <span className="text-gray-300 font-mono text-sm">{log}</span>;
    }

    // Check if message contains JSON
    const renderMessage = () => {
        // Always use extractAndColorizeJson to find and colorize JSON parts
        return extractAndColorizeJson(message);
    };

    const handleCopyClick = async () => {
        await navigator.clipboard.writeText(`${timestamp} ${level.toUpperCase()} [${service}] ${message}`);
        showToast(<SuccessToast title={t('toast.success')} message={t('toast.copied_to_clipboard')} />);
    };

    return (
        <div className="font-mono text-sm w-full flex flex-row items-end justify-between">
            <div className="inline-block">
                <span className="text-gray-500">{timestamp}</span>
                <span className="text-gray-300"> </span>
                <span className={`font-semibold ${getLogLevelColor(level)}`}>{level}</span>
                <span className="text-gray-300"> </span>
                <span className="text-purple-400">{service}</span>
                <span className="text-gray-300"> </span>
                {renderMessage()}
            </div>
            <span
                onClick={handleCopyClick}
                className="underline cursor-pointer text-gray-300 text-xs whitespace-nowrap"
            >
                copy line
            </span>
        </div>
    );
};
