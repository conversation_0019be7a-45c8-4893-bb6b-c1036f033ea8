import type { Meta, StoryObj } from '@storybook/react';
import { NextIntlClientProvider } from 'next-intl';
import { messages } from '@/.storybook/i18n-mock';
import { ItemBox } from '../ItemBox';
import { Deployment, DeploymentStatus, DeploymentType } from '@/types/deployments';
import { DeploymentsTypesEnum } from '@/common/deploymentsTypes';
import { WidgetDetailsOfDeployment } from './WidgetDetailsOfDeployment';
import { BlockchainNetworkName } from '@/types/blockchanConfigs';

const meta: Meta<typeof WidgetDetailsOfDeployment> = {
    title: 'Widgets/DetailsOfDeployment',
    component: WidgetDetailsOfDeployment,
    parameters: {
        layout: 'centered',
        nextjs: {
            appDirectory: true,
        },
    },
    decorators: [
        Story => (
            <NextIntlClientProvider messages={messages} locale="en">
                <ItemBox>
                    <Story />
                </ItemBox>
            </NextIntlClientProvider>
        ),
    ],
    tags: ['autodocs'],
};

export default meta;

type Story = StoryObj<typeof WidgetDetailsOfDeployment>;

const mockDeployment: Deployment = {
    id: 1,
    issuerName: 'Test Issuer',
    fullHost: 'test-issuer.example.com',
    status: DeploymentStatus.ACTIVE,
    userId: 123,
    deploymentType: DeploymentsTypesEnum.EMPE_MOCK_DEPLOYMENT,
    networkName: 'TESTNET' as BlockchainNetworkName,
    version: '1.0.0',
};

export const Default: Story = {
    args: {
        deployment: mockDeployment,
        type: DeploymentType.ISSUER,
        handleDelete: () => {},
    },
};
