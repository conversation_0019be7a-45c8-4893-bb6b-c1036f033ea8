import type { Meta, StoryObj } from '@storybook/react';
import { DashboardOptionBar } from './DashboardOptionBar';
import { messages } from '@/.storybook/i18n-mock';
import { NextIntlClientProvider } from 'next-intl';

const meta: Meta<typeof DashboardOptionBar> = {
    title: 'Components/DashboardOptionBar',
    component: DashboardOptionBar,
    parameters: {
        layout: 'fullscreen',
        nextjs: {
            appDirectory: true,
        },
    },
    decorators: [
        Story => (
            <NextIntlClientProvider locale="en" messages={messages}>
                <div className="bg-main-400">
                    <Story />
                </div>
            </NextIntlClientProvider>
        ),
    ],
    tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof DashboardOptionBar>;

export const Default: Story = {
    args: {
        credits: 9,
    },
};

export const RootDashboard: Story = {
    args: {
        credits: 9,
    },
};
