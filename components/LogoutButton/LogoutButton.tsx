'use client';

import { useAuthStore } from '@/store/authStore';
import { useUserStore } from '@/store/userStore';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import { useRouter } from 'next/navigation';

export const LogoutButton = () => {
    const t = useTranslations('dashboard');
    const router = useRouter();
    const { clearAuthTokens } = useAuthStore();
    const { clearUser } = useUserStore();

    const handleLogout = () => {
        clearAuthTokens();
        clearUser();
        router.replace('/');
    };

    return (
        <button id="components_LogoutButton_LogoutButton_button_ueucl9"
            className="flex items-center text-main-600 w-full gap-4 p-4 hover:bg-white/10 rounded-lg transition-all duration-300 ease-in-out"
            onClick={handleLogout}
        >
            <Image src="/assets/Logout.svg" alt="logout" width={24} height={24} />
            <span className="text-main-600">{t('logout')}</span>
        </button>
    );
};
