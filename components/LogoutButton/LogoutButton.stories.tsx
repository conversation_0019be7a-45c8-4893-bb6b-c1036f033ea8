import type { Meta, StoryObj } from '@storybook/react';
import { LogoutButton } from './LogoutButton';
import { NextIntlClientProvider } from 'next-intl';
import { messages } from '@/.storybook/i18n-mock';

const meta: Meta<typeof LogoutButton> = {
    title: 'Components/LogoutButton',
    component: LogoutButton,
    decorators: [
        Story => (
            <NextIntlClientProvider messages={messages} locale="en">
                <div className="bg-main-400 p-8">
                    <Story />
                </div>
            </NextIntlClientProvider>
        ),
    ],
    parameters: {
        layout: 'centered',
    },
};

export default meta;
type Story = StoryObj<typeof LogoutButton>;

export const Default: Story = {};
