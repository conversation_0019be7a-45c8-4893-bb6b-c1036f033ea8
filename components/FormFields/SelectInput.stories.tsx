import type { <PERSON>a, StoryObj } from '@storybook/react';
import { SelectInput } from './SelectInput';
import { useForm, Control } from 'react-hook-form';
import { NextIntlClientProvider } from 'next-intl';
import { messages } from '@/.storybook/i18n-mock';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const MockedControl = (args: any) => {
    const { control }: { control: Control } = useForm({
        defaultValues: {
            [args.name]: args.defaultValue,
        },
    });

    return <SelectInput {...args} control={control} />;
};

const meta: Meta<typeof SelectInput> = {
    title: 'FormFields/SelectInput',
    component: SelectInput,
    decorators: [
        Story => (
            <NextIntlClientProvider messages={messages} locale="en">
                <Story />
            </NextIntlClientProvider>
        ),
    ],
    tags: ['autodocs'],
    argTypes: {
        defaultValue: { control: 'text' },
        placeholder: { control: 'text' },
        label: { control: 'text' },
        disabled: { control: 'boolean' },
        className: { control: 'text' },
    },
};

export default meta;
type Story = StoryObj<typeof SelectInput>;

export const Default: Story = {
    render: args => <MockedControl {...args} />,
    args: {
        name: 'selectField',
        label: 'Wybierz opcję',
        placeholder: 'Wybierz...',
        options: [
            { value: 'option1', label: 'Opcja 1' },
            { value: 'option2', label: 'Opcja 2' },
            { value: 'option3', label: 'Opcja 3' },
        ],
    },
};

export const WithDefaultValue: Story = {
    render: args => <MockedControl {...args} />,
    args: {
        name: 'selectField',
        label: 'Pole z domyślną wartością',
        defaultValue: 'option2',
        options: [
            { value: 'option1', label: 'Opcja 1' },
            { value: 'option2', label: 'Opcja 2' },
            { value: 'option3', label: 'Opcja 3' },
        ],
    },
};

export const Disabled: Story = {
    render: args => <MockedControl {...args} />,
    args: {
        name: 'selectField',
        label: 'Pole nieaktywne',
        placeholder: 'Nie możesz wybrać',
        disabled: true,
        options: [
            { value: 'option1', label: 'Opcja 1' },
            { value: 'option2', label: 'Opcja 2' },
            { value: 'option3', label: 'Opcja 3' },
        ],
    },
};

export const WithCustomClassName: Story = {
    render: args => <MockedControl {...args} />,
    args: {
        name: 'selectField',
        label: 'Pole z własnym stylem',
        placeholder: 'Z własnym stylem...',
        className: 'border-2 border-blue-500',
        options: [
            { value: 'option1', label: 'Opcja 1' },
            { value: 'option2', label: 'Opcja 2' },
            { value: 'option3', label: 'Opcja 3' },
        ],
    },
};

export const WithoutLabel: Story = {
    render: args => <MockedControl {...args} />,
    args: {
        name: 'selectField',
        placeholder: 'Bez etykiety',
        options: [
            { value: 'option1', label: 'Opcja 1' },
            { value: 'option2', label: 'Opcja 2' },
            { value: 'option3', label: 'Opcja 3' },
        ],
    },
};
