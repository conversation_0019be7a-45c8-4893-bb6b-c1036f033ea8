import { InputHTMLAttributes, ReactNode } from 'react';
import clsx from 'clsx';

interface Props extends InputHTMLAttributes<HTMLInputElement> {
    label?: string;
    labelNode?: ReactNode;
    disabled?: boolean;
    postfix?: string;
}

export const TextInput = ({ className, disabled, label, labelNode, postfix, ...props }: Props) => {
    return (
        <div className="flex flex-col gap-1">
            {label && <label className="block !text-main-800 !text-sm">{label}</label>}
            {labelNode && <>{labelNode}</>}
            <div className="relative">
                <input
                    {...props}
                    disabled={disabled}
                    placeholder="Type here"
                    className={clsx(
                        'disabled:opacity-40 disabled:select-none',
                        'focus:outline-none focus:border-2',
                        'bg-main-600/5 border !text-main-600 w-full border-main-1100/50 rounded-lg p-3 h-12',
                        postfix ? 'pr-20' : 'pr-10',
                        className
                    )}
                />
                {postfix && (
                    <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-main-600/50 pointer-events-none select-none">
                        {postfix}
                    </div>
                )}
            </div>
        </div>
    );
};
