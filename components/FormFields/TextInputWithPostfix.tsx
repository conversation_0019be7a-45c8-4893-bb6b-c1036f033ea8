import { InputHTMLAttributes, ReactNode, useRef, useEffect, useState, forwardRef, useCallback } from 'react';
import clsx from 'clsx';

interface Props extends InputHTMLAttributes<HTMLInputElement> {
    label?: string;
    labelNode?: ReactNode;
    disabled?: boolean;
    postfix?: string;
    placeholder?: string;
}

export const TextInputWithPostfix = forwardRef<HTMLInputElement, Props>(
    ({ className, disabled, label, labelNode, postfix, value, onChange, placeholder = 'Type here', ...props }, ref) => {
        const inputRef = useRef<HTMLInputElement>(null);
        const canvasRef = useRef<HTMLCanvasElement>(null);
        const [textWidth, setTextWidth] = useState(0);
        const [placeholderWidth, setPlaceholderWidth] = useState(0);

        // Kombinuj referencje - używaj przekazanej ref lub wewnętrznej
        const combinedRef = useCallback(
            (node: HTMLInputElement | null) => {
                // Ustaw wewnętrzną referencję
                (inputRef as React.MutableRefObject<HTMLInputElement | null>).current = node;

                // Przekaż do zewnętrznej referencji
                if (typeof ref === 'function') {
                    ref(node);
                } else if (ref) {
                    (ref as React.MutableRefObject<HTMLInputElement | null>).current = node;
                }
            },
            [ref]
        );

        const measureTextWidth = (text: string) => {
            if (!canvasRef.current || !inputRef.current) return 0;

            const canvas = canvasRef.current;
            const context = canvas.getContext('2d');
            if (!context) return 0;

            const computedStyle = window.getComputedStyle(inputRef.current);
            context.font = `${computedStyle.fontSize} ${computedStyle.fontFamily}`;

            return context.measureText(text || '').width;
        };

        useEffect(() => {
            const currentValue = typeof value === 'string' ? value : inputRef.current?.value || '';
            setTextWidth(measureTextWidth(currentValue));

            // Mierz szerokość placeholdera
            if (placeholder && postfix) {
                setPlaceholderWidth(measureTextWidth(placeholder));
            }
        }, [value, placeholder, postfix]);

        const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
            setTextWidth(measureTextWidth(e.target.value));
            if (onChange) {
                onChange(e);
            }
        };

        const currentValue = typeof value === 'string' ? value : inputRef.current?.value || '';
        const isEmpty = !currentValue;

        return (
            <div className="flex flex-col gap-1">
                {label && <label className="block text-main-800 text-sm">{label}</label>}
                {labelNode && <>{labelNode}</>}
                <div className="relative">
                    <input
                        {...props}
                        ref={combinedRef}
                        maxLength={postfix ? 30 : undefined}
                        value={value}
                        onChange={handleInputChange}
                        disabled={disabled}
                        placeholder={postfix ? placeholder : placeholder}
                        className={clsx(
                            'disabled:opacity-80',
                            'focus:outline-none focus:border-2',
                            'bg-main-600/5 border !text-main-600 w-full border-main-1100/50 rounded-lg p-3 h-12',
                            postfix ? 'pr-20' : 'pr-10',
                            className
                        )}
                    />
                    {postfix && (
                        <div
                            className={clsx(
                                'absolute top-1/2 transform -translate-y-1/2 pt-0.5 pl-1 pointer-events-none select-none',
                                isEmpty ? 'text-gray-400' : 'text-main-600/50'
                            )}
                            style={{
                                left: isEmpty ? `${12 + placeholderWidth}px` : `${12 + textWidth}px`,
                                transition: 'left 0.1s ease',
                            }}
                        >
                            {postfix}
                        </div>
                    )}
                    {/* Ukryty canvas do mierzenia szerokości tekstu */}
                    <canvas
                        ref={canvasRef}
                        style={{
                            position: 'absolute',
                            visibility: 'hidden',
                            pointerEvents: 'none',
                        }}
                    />
                </div>
            </div>
        );
    }
);

TextInputWithPostfix.displayName = 'TextInputWithPostfix';
