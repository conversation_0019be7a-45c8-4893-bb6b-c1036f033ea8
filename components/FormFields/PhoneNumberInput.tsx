import { InputHTMLAttributes, ReactNode, useState, useEffect, useRef } from 'react';
import clsx from 'clsx';
import Image from 'next/image';

// Country codes mapping for auto-detection
const COUNTRY_CODES = {
    '+1': { name: 'US/CA', maxLength: 10 },
    '+7': { name: 'Russia', maxLength: 10 },
    '+20': { name: 'Egypt', maxLength: 10 },
    '+27': { name: 'South Africa', maxLength: 9 },
    '+30': { name: 'Greece', maxLength: 10 },
    '+31': { name: 'Netherlands', maxLength: 9 },
    '+32': { name: 'Belgium', maxLength: 9 },
    '+33': { name: 'France', maxLength: 10 },
    '+34': { name: 'Spain', maxLength: 9 },
    '+36': { name: 'Hungary', maxLength: 9 },
    '+39': { name: 'Italy', maxLength: 10 },
    '+40': { name: 'Romania', maxLength: 9 },
    '+41': { name: 'Switzerland', maxLength: 9 },
    '+43': { name: 'Austria', maxLength: 11 },
    '+44': { name: 'UK', maxLength: 10 },
    '+45': { name: 'Denmark', maxLength: 8 },
    '+46': { name: 'Sweden', maxLength: 9 },
    '+47': { name: 'Norway', maxLength: 8 },
    '+48': { name: 'Poland', maxLength: 9 },
    '+49': { name: 'Germany', maxLength: 11 },
    '+51': { name: 'Peru', maxLength: 9 },
    '+52': { name: 'Mexico', maxLength: 10 },
    '+53': { name: 'Cuba', maxLength: 8 },
    '+54': { name: 'Argentina', maxLength: 10 },
    '+55': { name: 'Brazil', maxLength: 11 },
    '+56': { name: 'Chile', maxLength: 9 },
    '+57': { name: 'Colombia', maxLength: 10 },
    '+58': { name: 'Venezuela', maxLength: 10 },
    '+60': { name: 'Malaysia', maxLength: 10 },
    '+61': { name: 'Australia', maxLength: 9 },
    '+62': { name: 'Indonesia', maxLength: 12 },
    '+63': { name: 'Philippines', maxLength: 10 },
    '+64': { name: 'New Zealand', maxLength: 9 },
    '+65': { name: 'Singapore', maxLength: 8 },
    '+66': { name: 'Thailand', maxLength: 9 },
    '+81': { name: 'Japan', maxLength: 11 },
    '+82': { name: 'South Korea', maxLength: 11 },
    '+84': { name: 'Vietnam', maxLength: 10 },
    '+86': { name: 'China', maxLength: 11 },
    '+90': { name: 'Turkey', maxLength: 10 },
    '+91': { name: 'India', maxLength: 10 },
    '+92': { name: 'Pakistan', maxLength: 10 },
    '+93': { name: 'Afghanistan', maxLength: 9 },
    '+94': { name: 'Sri Lanka', maxLength: 9 },
    '+95': { name: 'Myanmar', maxLength: 10 },
    '+98': { name: 'Iran', maxLength: 10 },
    '+212': { name: 'Morocco', maxLength: 9 },
    '+213': { name: 'Algeria', maxLength: 9 },
    '+216': { name: 'Tunisia', maxLength: 8 },
    '+218': { name: 'Libya', maxLength: 9 },
    '+233': { name: 'Ghana', maxLength: 9 },
    '+234': { name: 'Nigeria', maxLength: 10 },
    '+250': { name: 'Rwanda', maxLength: 9 },
    '+251': { name: 'Ethiopia', maxLength: 9 },
    '+252': { name: 'Somalia', maxLength: 8 },
    '+254': { name: 'Kenya', maxLength: 9 },
    '+255': { name: 'Tanzania', maxLength: 9 },
    '+256': { name: 'Uganda', maxLength: 9 },
    '+257': { name: 'Burundi', maxLength: 8 },
    '+351': { name: 'Portugal', maxLength: 9 },
    '+353': { name: 'Ireland', maxLength: 9 },
    '+354': { name: 'Iceland', maxLength: 7 },
    '+358': { name: 'Finland', maxLength: 10 },
    '+359': { name: 'Bulgaria', maxLength: 9 },
    '+370': { name: 'Lithuania', maxLength: 8 },
    '+371': { name: 'Latvia', maxLength: 8 },
    '+372': { name: 'Estonia', maxLength: 8 },
    '+373': { name: 'Moldova', maxLength: 8 },
    '+375': { name: 'Belarus', maxLength: 9 },
    '+380': { name: 'Ukraine', maxLength: 9 },
    '+381': { name: 'Serbia', maxLength: 9 },
    '+382': { name: 'Montenegro', maxLength: 8 },
    '+385': { name: 'Croatia', maxLength: 9 },
    '+386': { name: 'Slovenia', maxLength: 8 },
    '+387': { name: 'Bosnia', maxLength: 8 },
    '+389': { name: 'North Macedonia', maxLength: 8 },
    '+420': { name: 'Czech Republic', maxLength: 9 },
    '+421': { name: 'Slovakia', maxLength: 9 },
    '+502': { name: 'Guatemala', maxLength: 8 },
    '+503': { name: 'El Salvador', maxLength: 8 },
    '+504': { name: 'Honduras', maxLength: 8 },
    '+505': { name: 'Nicaragua', maxLength: 8 },
    '+506': { name: 'Costa Rica', maxLength: 8 },
    '+507': { name: 'Panama', maxLength: 8 },
    '+591': { name: 'Bolivia', maxLength: 8 },
    '+592': { name: 'Guyana', maxLength: 7 },
    '+593': { name: 'Ecuador', maxLength: 9 },
    '+595': { name: 'Paraguay', maxLength: 9 },
    '+597': { name: 'Suriname', maxLength: 7 },
    '+598': { name: 'Uruguay', maxLength: 8 },
    '+679': { name: 'Fiji', maxLength: 7 },
    '+855': { name: 'Cambodia', maxLength: 9 },
    '+856': { name: 'Laos', maxLength: 10 },
    '+880': { name: 'Bangladesh', maxLength: 10 },
    '+960': { name: 'Maldives', maxLength: 7 },
    '+961': { name: 'Lebanon', maxLength: 8 },
    '+962': { name: 'Jordan', maxLength: 9 },
    '+963': { name: 'Syria', maxLength: 9 },
    '+964': { name: 'Iraq', maxLength: 10 },
    '+965': { name: 'Kuwait', maxLength: 8 },
    '+966': { name: 'Saudi Arabia', maxLength: 9 },
    '+967': { name: 'Yemen', maxLength: 9 },
    '+968': { name: 'Oman', maxLength: 8 },
    '+971': { name: 'UAE', maxLength: 9 },
    '+972': { name: 'Israel', maxLength: 9 },
    '+973': { name: 'Bahrain', maxLength: 8 },
    '+974': { name: 'Qatar', maxLength: 8 },
    '+975': { name: 'Bhutan', maxLength: 8 },
    '+977': { name: 'Nepal', maxLength: 10 },
    '+1787': { name: 'Puerto Rico', maxLength: 7 },
    '+1809': { name: 'Dominican Rep.', maxLength: 7 },
    '+1876': { name: 'Jamaica', maxLength: 7 },
};

// Convert to options array for select and sort by country code numerically
const COUNTRY_OPTIONS = Object.entries(COUNTRY_CODES)
    .map(([code, info]) => ({
        value: code,
        label: `${code} (${info.name})`,
        numericCode: parseInt(code.replace('+', '')),
    }))
    .sort((a, b) => a.numericCode - b.numericCode);

interface Props extends Omit<InputHTMLAttributes<HTMLInputElement>, 'type'> {
    label?: string;
    labelNode?: ReactNode;
    disabled?: boolean;
    defaultCountryCode?: string;
    // For react-hook-form compatibility
    onChange?: (event: React.ChangeEvent<HTMLInputElement>) => void;
    value?: string; // Full phone number with country code
    name?: string;
}

export const PhoneNumberInput = ({
    className,
    disabled,
    label,
    labelNode,
    onChange,
    defaultCountryCode = '+1',
    value = '',
    name,
    ...props
}: Props) => {
    const [countryCode, setCountryCode] = useState(defaultCountryCode);
    const [phoneValue, setPhoneValue] = useState('');
    const [isSelectOpen, setIsSelectOpen] = useState(false);
    const [searchValue, setSearchValue] = useState('');
    const selectRef = useRef<HTMLDivElement>(null);
    const inputRef = useRef<HTMLInputElement>(null);

    // Helper function to create synthetic event for react-hook-form
    const createSyntheticEvent = (newValue: string): React.ChangeEvent<HTMLInputElement> => {
        return {
            target: {
                name: name || '',
                value: newValue,
            },
        } as React.ChangeEvent<HTMLInputElement>;
    };

    // Parse initial value
    useEffect(() => {
        if (value) {
            const detectedCode = detectCountryCode(value);
            if (detectedCode) {
                setCountryCode(detectedCode);
                setSearchValue(detectedCode.replace('+', ''));
                setPhoneValue(value.replace(detectedCode, '').trim());
            } else {
                setPhoneValue(value);
            }
        } else {
            // Set search value based on current country code
            setSearchValue(countryCode.replace('+', ''));
        }
    }, [value, countryCode]);

    // Handle click outside to close select
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (selectRef.current && !selectRef.current.contains(event.target as Node)) {
                setIsSelectOpen(false);
            }
        };

        if (isSelectOpen) {
            document.addEventListener('mousedown', handleClickOutside);
            return () => document.removeEventListener('mousedown', handleClickOutside);
        }
    }, [isSelectOpen]);

    const detectCountryCode = (fullNumber: string): string | null => {
        // Sort by length (longest first) to match longer codes first
        const sortedCodes = Object.keys(COUNTRY_CODES).sort((a, b) => b.length - a.length);

        for (const code of sortedCodes) {
            if (fullNumber.startsWith(code)) {
                return code;
            }
        }
        return null;
    };

    const formatPhoneNumber = (input: string, code: string) => {
        const digits = input.replace(/\D/g, '');
        const countryInfo = COUNTRY_CODES[code as keyof typeof COUNTRY_CODES];
        const maxLength = countryInfo?.maxLength || 15;

        // Limit digits based on country
        const limitedDigits = digits.slice(0, maxLength);

        if (code === '+1') {
            // US format: (XXX) XXX-XXXX
            if (limitedDigits.length <= 3) return limitedDigits;
            if (limitedDigits.length <= 6) return `(${limitedDigits.slice(0, 3)}) ${limitedDigits.slice(3)}`;
            return `(${limitedDigits.slice(0, 3)}) ${limitedDigits.slice(3, 6)}-${limitedDigits.slice(6)}`;
        } else {
            // Default format: XXX XXX XXX
            if (limitedDigits.length <= 3) return limitedDigits;
            if (limitedDigits.length <= 6) return `${limitedDigits.slice(0, 3)} ${limitedDigits.slice(3)}`;
            return `${limitedDigits.slice(0, 3)} ${limitedDigits.slice(3, 6)} ${limitedDigits.slice(6)}`;
        }
    };

    const handleCountryCodeSelect = (code: string) => {
        setCountryCode(code);
        setSearchValue(code.replace('+', ''));
        setIsSelectOpen(false);

        // Reformat phone number for new country
        const cleanPhone = phoneValue.replace(/\D/g, '');
        const formatted = formatPhoneNumber(cleanPhone, code);
        setPhoneValue(formatted);

        // Create full phone number and notify react-hook-form
        const fullNumber = code + cleanPhone;
        onChange?.(createSyntheticEvent(fullNumber));
    };

    // Filter country options based on search value
    const filteredOptions = COUNTRY_OPTIONS.filter(option => {
        if (!searchValue) return true;
        const searchDigits = searchValue.replace(/\D/g, '');
        const codeDigits = option.value.replace('+', '');
        return codeDigits.startsWith(searchDigits);
    });

    const handleCountryCodeInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const input = e.target.value;
        // Allow only digits
        const digits = input.replace(/\D/g, '');
        setSearchValue(digits);

        // Auto-select if exact match found
        const exactMatch = COUNTRY_OPTIONS.find(option => option.value === `+${digits}`);

        if (exactMatch) {
            setCountryCode(exactMatch.value);
            // Reformat phone number for new country
            const cleanPhone = phoneValue.replace(/\D/g, '');
            const formatted = formatPhoneNumber(cleanPhone, exactMatch.value);
            setPhoneValue(formatted);

            // Create full phone number and notify react-hook-form
            const fullNumber = exactMatch.value + cleanPhone;
            onChange?.(createSyntheticEvent(fullNumber));
        }

        // Show dropdown when typing
        if (!isSelectOpen) {
            setIsSelectOpen(true);
        }
    };

    const handleCountryCodeInputFocus = () => {
        if (!disabled) {
            setIsSelectOpen(true);
        }
    };

    const handleCountryCodeInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
        if (e.key === 'Escape') {
            setIsSelectOpen(false);
            inputRef.current?.blur();
        } else if (e.key === 'Enter') {
            e.preventDefault();
            if (filteredOptions.length > 0) {
                handleCountryCodeSelect(filteredOptions[0].value);
            }
        } else if (e.key === 'ArrowDown') {
            e.preventDefault();
            if (!isSelectOpen) {
                setIsSelectOpen(true);
            }
        }
    };

    const handleSelectToggle = () => {
        if (!disabled) {
            setIsSelectOpen(prev => !prev);
            if (!isSelectOpen) {
                // Focus input when opening
                setTimeout(() => inputRef.current?.focus(), 0);
            }
        }
    };

    const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const input = e.target.value;
        const formatted = formatPhoneNumber(input, countryCode);
        const cleanPhone = formatted.replace(/\D/g, '');

        setPhoneValue(formatted);

        // Create full phone number and notify react-hook-form
        const fullNumber = countryCode + cleanPhone;
        onChange?.(createSyntheticEvent(fullNumber));
    };

    return (
        <div className="flex flex-col gap-1">
            {label && <label className="block text-main-800 text-sm">{label}</label>}
            {labelNode && <>{labelNode}</>}

            {/* Hidden input for react-hook-form */}
            <input type="hidden" name={name} value={countryCode + phoneValue.replace(/\D/g, '')} onChange={onChange} />

            <div className="flex gap-2">
                {/* Country Code Search Select */}
                <div className="relative w-36" ref={selectRef}>
                    <div className="relative">
                        <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-main-600 font-medium text-sm pointer-events-none">
                            +
                        </div>
                        <input
                            ref={inputRef}
                            type="text"
                            disabled={disabled}
                            value={searchValue}
                            onChange={handleCountryCodeInputChange}
                            onFocus={handleCountryCodeInputFocus}
                            onKeyDown={handleCountryCodeInputKeyDown}
                            placeholder="48"
                            className={clsx(
                                'disabled:opacity-80',
                                'focus:outline-none focus:border-2',
                                'bg-main-600/5 border text-main-600 w-full border-main-1100/50 rounded-lg h-12',
                                'pl-6 pr-10 py-3',
                                disabled && 'cursor-not-allowed opacity-50',
                                className
                            )}
                        />
                        <div
                            onClick={handleSelectToggle}
                            className={clsx(
                                'absolute right-3 top-1/2 transform -translate-y-1/2 cursor-pointer',
                                disabled && 'cursor-not-allowed opacity-50'
                            )}
                        >
                            <Image
                                height={16}
                                width={16}
                                alt=""
                                src="/icons/arrow.svg"
                                className={clsx(
                                    'transition-transform duration-300',
                                    isSelectOpen ? 'rotate-180' : 'rotate-0'
                                )}
                            />
                        </div>
                    </div>

                    {/* Dropdown */}
                    {isSelectOpen && (
                        <div className="absolute top-14 z-10 left-0 w-full max-h-48 without-scrollbar overflow-y-auto bg-main-1300 border text-main-600 border-main-1100/50 rounded-lg p-2 shadow-lg">
                            {filteredOptions.length > 0 ? (
                                filteredOptions.map(option => (
                                    <div
                                        key={option.value}
                                        className="cursor-pointer hover:bg-main-600/10 p-2 rounded text-sm"
                                        onClick={() => handleCountryCodeSelect(option.value)}
                                    >
                                        {option.label}
                                    </div>
                                ))
                            ) : (
                                <div className="p-2 text-sm text-main-600/60">Nie znaleziono kierunkowego</div>
                            )}
                        </div>
                    )}
                </div>

                {/* Phone Number Input */}
                <div className="relative flex-1">
                    <input
                        {...props}
                        type="tel"
                        disabled={disabled}
                        value={phoneValue}
                        onChange={handlePhoneChange}
                        placeholder="123 456 789"
                        className={clsx(
                            'disabled:opacity-80',
                            'focus:outline-none focus:border-2',
                            'bg-main-600/5 border !text-main-600 w-full border-main-1100/50 rounded-lg p-3 h-12',
                            className
                        )}
                    />
                </div>
            </div>
        </div>
    );
};
