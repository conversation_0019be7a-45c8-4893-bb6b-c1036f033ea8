import type { Meta, StoryObj } from '@storybook/react';
import { TextInput } from './TextInput';

const meta: Meta<typeof TextInput> = {
    title: 'FormFields/TextInput',
    component: TextInput,
    tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
    args: {
        placeholder: 'Wpisz tekst...',
        label: 'Pole tekstowe',
    },
};

export const Disabled: Story = {
    args: {
        disabled: true,
        placeholder: 'Pole wyłączone',
        label: 'Pole nieaktywne',
    },
};

export const WithCustomClassName: Story = {
    args: {
        className: 'border-2 border-blue-500',
        placeholder: 'Z własną klasą CSS',
        label: 'Pole z własnym stylem',
    },
};

export const WithoutLabel: Story = {
    args: {
        placeholder: 'Bez etykiety',
    },
};
