import type { Meta, StoryObj } from '@storybook/react';
import { PasswordInput } from './PasswordInput';

const meta: Meta<typeof PasswordInput> = {
    title: 'FormFields/PasswordInput',
    component: PasswordInput,
    tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
    args: {
        placeholder: 'Wprowadź hasło',
    },
};

export const WithLabel: Story = {
    args: {
        label: 'Hasło',
        placeholder: 'Wprowad<PERSON> hasło',
    },
};

export const Disabled: Story = {
    args: {
        label: 'Hasł<PERSON>',
        placeholder: '<PERSON><PERSON>row<PERSON><PERSON> hasło',
        disabled: true,
    },
};
