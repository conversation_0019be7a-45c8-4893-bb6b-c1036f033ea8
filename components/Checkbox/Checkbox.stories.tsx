import React from 'react';
import { Meta, StoryFn } from '@storybook/react';
import { useForm, FormProvider } from 'react-hook-form';
import { NextIntlClientProvider } from 'next-intl';
import { Checkbox } from './Checkbox';

const messages = {
    exampleCheckbox: 'I agree to the terms and conditions',
    disabledCheckbox: 'I agree to the terms and conditions (disabled)',
};

export default {
    title: 'Components/Checkbox',
    component: Checkbox,
    decorators: [
        Story => (
            <NextIntlClientProvider locale="en" messages={messages}>
                <Story />
            </NextIntlClientProvider>
        ),
    ],
    argTypes: {
        name: { control: 'text' },
        label: { control: 'text' },
        disabled: { control: 'boolean' },
        defaultValue: { control: 'boolean' },
    },
} as Meta;

const FormWrapper = ({ children }: { children: React.ReactNode }) => {
    const methods = useForm({
        defaultValues: { exampleCheckbox: false },
    });

    return <FormProvider {...methods}>{children}</FormProvider>;
};

const Template: StoryFn = () => (
    <FormWrapper>
        <Checkbox name="terms" label="I agree to the terms and conditions" defaultValue={false} />
    </FormWrapper>
);

export const Default = Template.bind({});
Default.args = {
    name: 'exampleCheckbox',
    label: 'I agree to the terms and conditions',
    disabled: false,
    defaultValue: false,
};

export const Disabled = Template.bind({});
Disabled.args = {
    name: 'disabledCheckbox',
    label: 'I agree to the terms and conditions',
    disabled: true,
    defaultValue: true,
};
