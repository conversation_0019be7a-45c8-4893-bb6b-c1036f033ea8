'use client';

import Image from 'next/image';
import { motion } from 'motion/react';

export const AnimLogo = () => {
    return (
        <motion.div
            initial={{ opacity: 0 }}
            animate={{
                opacity: 1,
            }}
            transition={{
                delay: 1,
                duration: 3,
                ease: 'easeInOut',
                times: [0, 1],
            }}
            className="w-full"
        >
            <Image
                src="/assets/MainLogo.svg"
                alt="Empe One Click Deployer Logo"
                width={300}
                height={100}
                className="w-2/3 mx-auto max-w-[600px]"
            />
        </motion.div>
    );
};
