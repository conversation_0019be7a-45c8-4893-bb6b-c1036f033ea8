import { AxiosError } from 'axios';

export const getErrorStatus = (error: unknown): number => {
    if (error instanceof AxiosError) {
        const response = error.response;

        if (response?.data.code) {
            return response.data.code;
        }

        if (response?.status) {
            return response.status;
        }
    }

    return 500;
};

export const getErrorMessage = (error: unknown): string => {
    if (error instanceof AxiosError) {
        const response = error.response;

        if (response?.data.message) {
            return response.data.message;
        }

        if (response?.statusText) {
            return response.statusText;
        }
    }

    if (error instanceof Error) {
        return error.message;
    }

    return 'Unknown error';
};
