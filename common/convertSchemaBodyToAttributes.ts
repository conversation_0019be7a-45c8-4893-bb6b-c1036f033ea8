import { IssuerSchema } from '@/types/issuerSchema';
import { SchemaAttribute } from '@/types/schemaManager';

export const convertSchemaBodyToAttributes = (schemaBody: IssuerSchema['schemaBody']): SchemaAttribute[] => {
    // Add safety checks for nested properties
    if (!schemaBody || !schemaBody.properties || !schemaBody.properties.credentialSubject) {
        console.warn('Invalid schemaBody structure:', schemaBody);
        return [];
    }

    const credentialSubjectProperties = schemaBody.properties.credentialSubject.properties;
    const requiredFields = schemaBody.properties.credentialSubject.required || [];

    // Check if credentialSubjectProperties exists and is an object
    if (!credentialSubjectProperties || typeof credentialSubjectProperties !== 'object') {
        console.warn('credentialSubjectProperties is not valid:', credentialSubjectProperties);
        return [];
    }

    const convertPropertiesToAttributes = (
        properties: Record<string, any>,
        required: string[] = []
    ): SchemaAttribute[] => {
        // Add safety check for properties parameter
        if (!properties || typeof properties !== 'object') {
            console.warn('Properties parameter is not valid:', properties);
            return [];
        }

        return Object.entries(properties).map(([key, value], index) => {
            const attribute: SchemaAttribute = {
                id: `attr_${Date.now()}_${index}`,
                name: key,
                title: value?.title || key,
                dataType: value?.type || 'string',
                description: value?.description || '',
                format: value?.format,
                required: required.includes(key),
            };

            // Handle nested properties for object types
            if (value?.type === 'object' && value?.properties) {
                attribute.properties = convertPropertiesToAttributes(value.properties, value.required || []);
            }

            return attribute;
        });
    };

    return convertPropertiesToAttributes(credentialSubjectProperties, requiredFields);
};
