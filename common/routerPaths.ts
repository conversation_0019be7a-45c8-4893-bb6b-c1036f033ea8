export enum RouterPaths {
    START_SCREEN = '/',
    LOGIN = '/',
    SIGNUP = '/signup',
    REQUEST_PASSWORD_RESET = '/request-password-reset',
    CHANGE_PASSWORD = '/change-password',
    DASHBOARD = '/dashboard',
    PROFILE = '/dashboard/profile',
    CONFIRM_EMAIL = '/confirm-email',
    PAYMENT = '/dashboard/payment',
    ISSUER_DETAILS = '/dashboard/issuer-details',
    VERIFIER_DETAILS = '/dashboard/verifier-details',
    NEW_ISSUER = '/dashboard/new-issuer',
    NEW_VERIFIER = '/dashboard/new-verifier',
    DASHBOARD_TERMS_AND_CONDITIONS = '/dashboard/terms-and-conditions',
    DASHBOARD_POLICY = '/dashboard/policy',
    TERMS_AND_CONDITIONS = '/signup/terms-and-conditions',
    POLICY = '/signup/policy',
}
