import {
    handleGetUserIssuerDeployments,
    handleGetIssuerDeploymentById,
    handleDeleteIssuerDeployment,
} from '@/api/deploymentsIssuer';
import {
    handleGetUserVerifierDeployments,
    handleGetVerifierDeploymentById,
    handleDeleteVerifierDeployment,
} from '@/api/deploymentsVerifier';
import { DeploymentType } from '@/types/deployments';
import { handleGetIssuerLogs, handleGetVerifierLogs } from '@/api/logs';

export const fetchDeploymentsMethods = {
    [DeploymentType.ISSUER]: handleGetUserIssuerDeployments,
    [DeploymentType.VERIFIER]: handleGetUserVerifierDeployments,
};

export const fetchDeploymentDetailsMethods = {
    [DeploymentType.ISSUER]: handleGetIssuerDeploymentById,
    [DeploymentType.VERIFIER]: handleGetVerifierDeploymentById,
};

export const deleteDeploymentMethods = {
    [DeploymentType.ISSUER]: handleDeleteIssuerDeployment,
    [DeploymentType.VERIFIER]: handleDeleteVerifierDeployment,
};

export const fetchLogsMethods = {
    [DeploymentType.ISSUER]: handleGetIssuerLogs,
    [DeploymentType.VERIFIER]: handleGetVerifierLogs,
};
