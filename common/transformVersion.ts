import { BlockchainConfig } from '@/types/blockchanConfigs';
import { SelectValue } from '@/types/selectValue';
import { VersionInfo } from '@/types/version';

const compareVersions = (version1: string, version2: string): number => {
    // Rozdziel wersję na główną część i sufiks (jeśli istnieje)
    const parseVersion = (version: string) => {
        const [mainVersion, suffix] = version.split('-');
        const parts = mainVersion.split('.').map(Number);
        return { parts, suffix: suffix || null };
    };

    const v1 = parseVersion(version1);
    const v2 = parseVersion(version2);

    // Porównaj główne części wersji
    const length = Math.max(v1.parts.length, v2.parts.length);

    for (let i = 0; i < length; i++) {
        const part1 = i < v1.parts.length ? v1.parts[i] : 0;
        const part2 = i < v2.parts.length ? v2.parts[i] : 0;

        if (part1 < part2) return 1;
        if (part1 > part2) return -1;
    }

    // Jeśli główne części są równe, porównaj sufiksy
    if (v1.suffix === null && v2.suffix === null) return 0;
    if (v1.suffix === null && v2.suffix !== null) return -1; // 1.0.0 > 1.0.0-rc1
    if (v1.suffix !== null && v2.suffix === null) return 1; // 1.0.0-rc1 < 1.0.0

    // Oba mają sufiksy - porównaj je
    if (v1.suffix! < v2.suffix!) return 1;
    if (v1.suffix! > v2.suffix!) return -1;
    return 0;
};

export const sortVersions = (versions: Array<VersionInfo>) => {
    return versions.sort((a, b) => compareVersions(a.version, b.version));
};

export const transformVersion = (data: Array<VersionInfo>): Array<SelectValue> => {
    return data.map(item => ({
        label: item.version,
        value: item.id,
    }));
};

export const sortedTransformedVersions = (data: Array<VersionInfo>) => {
    const sortedVersions = sortVersions(data);
    return transformVersion(sortedVersions);
};

export const availableSortedTransformedVersions = (data: Array<VersionInfo>, currentVersion?: string) => {
    let versions = data;

    if (currentVersion) {
        versions = data.filter(item => compareVersions(item.version, currentVersion) === -1);
    }

    const sortedVersions = sortVersions(versions);
    return transformVersion(sortedVersions);
};

export const availableTransformedBlockchainConfigs = (data: Array<BlockchainConfig>) => {
    let blockchainConfigs = data;

    return blockchainConfigs.map(item => ({
        label: item.type,
        value: item.id,
    }));
};
