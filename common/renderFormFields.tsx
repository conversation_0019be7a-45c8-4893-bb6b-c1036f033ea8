import { DateTimeInput, PasswordInput, PinCodeInput, SelectInput, TextInput } from '@/components/FormFields';
import { camelToSnakeCase } from './camelToSnakeCase';
import { FormValidationError } from '@/components/FormValidationError';
import { Control, FieldValues } from 'react-hook-form';
import { FormFieldsTypes } from './formFieldsTypes';
import { SelectValue } from '@/types/selectValue';
import { Checkbox } from '@/components/Checkbox';
import { PhoneNumberInput } from '@/components/FormFields/PhoneNumberInput';
import { TextInputWithPostfix } from '@/components/FormFields/TextInputWithPostfix';
import { useTranslations } from 'next-intl';

// Funkcja do formatowania camelCase na czytelny tekst
const formatCamelCaseToReadable = (text: string): string => {
    return (
        text
            // Dodaj spację przed wielką literą (ale nie na początku)
            .replace(/([a-z])([A-Z])/g, '$1 $2')
            // Pierwsza litera wielka
            .replace(/^./, str => str.toUpperCase())
    );
};

type FormFields<T> = {
    [K in keyof T]: any;
};

type RegisterFunction<T> = (field: keyof T) => any;

interface RenderFormFieldsProps<T extends FieldValues> {
    formFields: FormFields<T>;
    postfix?: Partial<T>;
    register: RegisterFunction<T>;
    errors: Partial<Record<keyof T, { message?: string }>>;
    t: ReturnType<typeof useTranslations>;
    formInputsTypes: Record<keyof T, FormFieldsTypes>;
    trigger?: (field: keyof T) => void;
    control?: Control<T>;
    selectOptions?: Record<any, Array<SelectValue>>;
    checkboxHref?: Record<any, string>;
    disabledFields?: Record<keyof T, boolean>;
    defaultValues?: Partial<T>;
    skipTranslation?: boolean; // Nowa opcja do pomijania tłumaczeń
}

export const renderFormFields = <T extends FieldValues>({
    formFields,
    register,
    errors,
    t,
    formInputsTypes,
    trigger,
    control,
    selectOptions,
    checkboxHref,
    postfix,
    disabledFields,
    defaultValues,
    skipTranslation = false,
}: RenderFormFieldsProps<T>) => {
    return Object.entries(formFields).map(([key]) => {
        const fieldKey = key as keyof T;
        const localeKey = camelToSnakeCase(key) + '_label';
        const fieldType = formInputsTypes[fieldKey];
        const handleBlur = trigger ? () => trigger(fieldKey) : undefined;
        const isDisabled = disabledFields?.[fieldKey] ?? false;
        const labelText = skipTranslation ? formatCamelCaseToReadable(key) : t(localeKey);

        if (fieldType === FormFieldsTypes.SELECT && selectOptions && selectOptions[fieldKey]) {
            return (
                <div key={key} className="flex flex-col gap-1">
                    <SelectInput
                        {...register(fieldKey)}
                        label={labelText}
                        onBlur={handleBlur}
                        control={control}
                        defaultValue={defaultValues?.[fieldKey]}
                        options={selectOptions[fieldKey]}
                        disabled={isDisabled}
                    />
                    <FormValidationError message={errors[fieldKey]?.message} />
                </div>
            );
        }
        if (fieldType === FormFieldsTypes.DATETIME) {
            return (
                <div key={key} className="flex flex-col gap-1">
                    <DateTimeInput
                        {...register(fieldKey)}
                        label={labelText}
                        onBlur={handleBlur}
                        control={control}
                        disabled={isDisabled}
                    />
                    <FormValidationError message={errors[fieldKey]?.message} />
                </div>
            );
        }
        if (fieldType === FormFieldsTypes.PASSWORD) {
            return (
                <div key={key} className="flex flex-col gap-1">
                    <PasswordInput
                        {...register(fieldKey)}
                        label={labelText}
                        onBlur={handleBlur}
                        disabled={isDisabled}
                    />
                    <FormValidationError message={errors[fieldKey]?.message} />
                </div>
            );
        }
        if (fieldType === FormFieldsTypes.PIN_CODE) {
            return (
                <div key={key} className="flex flex-col gap-1">
                    <PinCodeInput
                        {...register(fieldKey)}
                        label={labelText}
                        onBlur={handleBlur}
                        defaultValue={defaultValues?.[fieldKey] as string}
                        disabled={isDisabled}
                    />
                    <FormValidationError message={errors[fieldKey]?.message} />
                </div>
            );
        }
        if (fieldType === FormFieldsTypes.PHONE) {
            return (
                <div key={key} className="flex flex-col gap-1">
                    <PhoneNumberInput
                        {...register(fieldKey)}
                        label={labelText}
                        onBlur={handleBlur}
                        disabled={isDisabled}
                    />
                    <FormValidationError message={errors[fieldKey]?.message} />
                </div>
            );
        }

        if (fieldType === FormFieldsTypes.INPUT_WITH_POSTFIX) {
            const fieldPostfix = postfix?.[fieldKey] as string | undefined;
            return (
                <div key={key} className="flex flex-col gap-1">
                    <TextInputWithPostfix
                        {...register(fieldKey)}
                        label={labelText}
                        onBlur={handleBlur}
                        disabled={isDisabled}
                        postfix={fieldPostfix}
                    />
                    <FormValidationError message={errors[fieldKey]?.message} />
                </div>
            );
        }

        if (fieldType === FormFieldsTypes.CHECKBOX) {
            return (
                <div key={key} className="flex flex-col gap-1">
                    <Checkbox
                        {...register(fieldKey)}
                        mainLabel={labelText}
                        href={checkboxHref?.[fieldKey]}
                        control={control}
                        disabled={isDisabled}
                    />
                    <FormValidationError message={errors[fieldKey]?.message} />
                </div>
            );
        } else {
            return (
                <div key={key} className="flex flex-col gap-1">
                    <TextInput {...register(fieldKey)} label={labelText} onBlur={handleBlur} disabled={isDisabled} />
                    <FormValidationError message={errors[fieldKey]?.message} />
                </div>
            );
        }
    });
};
