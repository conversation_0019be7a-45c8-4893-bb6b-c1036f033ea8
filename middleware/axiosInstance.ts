import { handleTokenRefresh } from '@/api/user';
import { useAuthStore } from '@/store/authStore';
import axios, { AxiosError, InternalAxiosRequestConfig } from 'axios';
import { useConfigStore } from '@/store/configStore';

const AUTH_EXCLUDE_ENDPOINTS = ['/auth/login', '/auth/signup', '/auth/refresh'];

interface ExtendedAxiosRequestConfig extends InternalAxiosRequestConfig {
    _retry?: boolean;
}

// Funkcja pomocnicza do pobierania aktualnego baseURL
const getBaseURL = (): string => {
    const { env } = useConfigStore.getState();
    return env.API_URL || '';
};

const axiosInstance = axios.create({
    headers: {
        'Content-Type': 'application/json',
    },
});

const refreshAuthToken = async () => {
    try {
        const refreshToken = useAuthStore.getState().refreshToken;
        const response = await handleTokenRefresh(refreshToken);
        const { token, refreshToken: newRefreshToken, zendeskToken } = response;
        useAuthStore.getState().setAuthTokens(token, newRefreshToken, zendeskToken);
        return token;
    } catch (error) {
        useAuthStore.getState().clearAuthTokens();
        throw error;
    }
};

// --- Nowe zmienne globalne dla kolejkowania ---
let isRefreshing = false;
let failedQueue: {
    resolve: (value?: unknown) => void;
    reject: (reason?: unknown) => void;
}[] = [];

const processQueue = (error: any, token: string | null = null) => {
    failedQueue.forEach(promise => {
        if (error) {
            promise.reject(error);
        } else {
            promise.resolve(token);
        }
    });
    failedQueue = [];
};

// --- Interceptor żądań ---
axiosInstance.interceptors.request.use(config => {
    // Dynamicznie ustawiamy baseURL przy każdym żądaniu
    const baseURL = getBaseURL();
    if (baseURL && !config.baseURL) {
        config.baseURL = baseURL;
    }

    const isPublicEndpoint = AUTH_EXCLUDE_ENDPOINTS.some(endpoint => config.url?.includes(endpoint));

    if (!isPublicEndpoint) {
        const token = useAuthStore.getState().token;
        if (token) {
            config.headers.Authorization = `Bearer ${token}`;
        }
    }

    return config;
});

// --- Interceptor odpowiedzi ---
axiosInstance.interceptors.response.use(
    response => response,
    async (error: AxiosError) => {
        const originalRequest = error.config as ExtendedAxiosRequestConfig;

        const isPublicEndpoint = AUTH_EXCLUDE_ENDPOINTS.some(endpoint => error.config?.url?.includes(endpoint));

        if (error.response?.status === 401 && !isPublicEndpoint && originalRequest && !originalRequest._retry) {
            if (isRefreshing) {
                return new Promise((resolve, reject) => {
                    failedQueue.push({
                        resolve: token => {
                            if (originalRequest.headers) {
                                originalRequest.headers.Authorization = `Bearer ${token}`;
                            }
                            resolve(axiosInstance(originalRequest));
                        },
                        reject: err => reject(err),
                    });
                });
            }

            originalRequest._retry = true;
            isRefreshing = true;

            try {
                const newToken: string = await refreshAuthToken();
                processQueue(null, newToken);

                if (originalRequest.headers) {
                    originalRequest.headers.Authorization = `Bearer ${newToken}`;
                }

                return axiosInstance(originalRequest);
            } catch (refreshError) {
                processQueue(refreshError, null);
                return Promise.reject(refreshError);
            } finally {
                isRefreshing = false;
            }
        }

        return Promise.reject(error);
    }
);

export default axiosInstance;
