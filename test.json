"schemaBody": {
      "$metadata": {
        "version": "1",
        "type": "BirthCertificate"
      },
      "title": "BirthCertificate",
      "properties": {
        "credentialSubject": {
          "description": "Stores the data of the credential",
          "title": "Credential subject",
          "properties": {
            "firstName": {
              "description": "First name on the birth certificate",
              "title": "First name on the birth certificate",
              "type": "string"
            },
            "middleName": {
              "description": "Middle name on the birth certificate",
              "title": "Middle name on the birth certificate",
              "type": "string"
            },
            "lastName": {
              "description": "Last name on the birth certificate",
              "title": "Last name on the birth certificate",
              "type": "string"
            },
            "gender": {
              "description": "Gender of the person",
              "title": "Gender of the person",
              "type": "string",
              "enum": [
                "Male",
                "Female",
                "Other"
              ]
            },
            "birthDate": {
              "description": "Date of birth",
              "title": "Date of birth",
              "type": "string",
              "format": "date"
            },
            "birthPlaceCountry": {
              "description": "Country of birth",
              "title": "Country of birth",
              "type": "string"
            },
            "fatherFirstName": {
              "description": "First name of the father",
              "title": "First name of the father",
              "type": "string"
            },
            "fatherMiddleName": {
              "description": "Middle name of the father",
              "title": "Middle name of the father",
              "type": "string"
            },
            "fatherLastName": {
              "description": "Last name of the father",
              "title": "Last name of the father",
              "type": "string"
            },
            "motherFirstName": {
              "description": "First name of the mother",
              "title": "First name of the mother",
              "type": "string"
            },
            "motherMiddleName": {
              "description": "Middle name of the mother",
              "title": "Middle name of the mother",
              "type": "string"
            },
            "motherLastName": {
              "description": "Last name of the mother",
              "title": "Last name of the mother",
              "type": "string"
            },
            "issuedAt": {
              "description": "Timestamp for issuance",
              "title": "Timestamp for issuance",
              "type": "string",
              "format": "date-time"
            }
          },
          "required": [
            "firstName",
            "lastName",
            "gender",
            "birthDate",
            "birthPlaceCountry",
            "birthPlaceCity",
            "motherFirstName",
            "motherLastName",
            "issuedAt"
          ],
          "type": "object"
        },
        "@context": {
          "type": [
            "string",
            "array",
            "object"
          ]
        },
        "id": {
          "type": "string"
        },
        "issuanceDate": {
          "format": "date-time",
          "type": "string"
        },
        "issuer": {
          "type": [
            "string",
            "object"
          ],
          "format": "uri",
          "properties": {
            "id": {
              "format": "uri",
              "type": "string"
            }
          },
          "required": [
            "id"
          ]
        },
        "type": {
          "type": [
            "string",
            "array"
          ],
          "items": {
            "type": "string"
          }
        },
        "credentialSchema": {
          "properties": {
            "id": {
              "format": "uri",
              "type": "string"
            },
            "type": {
              "type": "string"
            }
          },
          "required": [
            "id",
            "type"
          ],
          "type": "object"
        }
      },
      "required": [
        "credentialSubject",
        "@context",
        "id",
        "issuanceDate",
        "issuer",
        "type",
        "credentialSchema"
      ],
      "type": "object"
    }