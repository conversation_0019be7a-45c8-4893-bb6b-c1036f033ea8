// src/api/logs.ts
import axiosInstance from '@/middleware/axiosInstance';

/**
 * GET /logs/issuer?issuerId={id}&startTime={iso}&endTime={iso}
 */
export const handleGetIssuerLogs = async (
    issuerId: string,
    startTime?: number | string, // ISO-8601, e.g. "2025-06-13T09:30:00Z"
    endTime?: number | string, // ISO-8601
    search?: string
): Promise<string[]> => {
    const payload: any = {
        issuerId,
    };

    if (startTime !== undefined) {
        payload.startTime = startTime;
    }

    if (endTime !== undefined) {
        payload.endTime = endTime;
    }

    if (search && search.length > 0) {
        payload.phrase = search;
    }

    const res = await axiosInstance.get('/logs/issuer', {
        params: payload,
    });
    return res.data;
};

/**
 * GET /logs/verifier?verifierId={id}&startTime={iso}&endTime={iso}
 */
export const handleGetVerifierLogs = async (
    verifierId: string,
    startTime?: number | string,
    endTime?: number | string,
    search?: string
): Promise<string[]> => {
    const payload: any = {
        verifierId,
    };

    if (startTime !== undefined) {
        payload.startTime = startTime;
    }

    if (endTime !== undefined) {
        payload.endTime = endTime;
    }

    if (search && search.length > 0) {
        payload.phrase = search;
    }

    const res = await axiosInstance.get('/logs/verifier', {
        params: payload,
    });
    return res.data;
};
