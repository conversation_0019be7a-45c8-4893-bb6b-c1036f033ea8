import axiosInstance from '@/middleware/axiosInstance';
import { IssuerDeployment } from '@/types/deploymentsIssuer';
import { VersionInfo } from '@/types/version';

export const handleGetAvailableIssuerVersions = async (): Promise<Array<VersionInfo>> => {
    const response = await axiosInstance.get(`/issuer-version`);
    return response.data;
};

export const handleGetIssuerVersionById = async (id: string): Promise<VersionInfo> => {
    const response = await axiosInstance.get(`/issuer-version/${id}`);
    return response.data;
};

export const handleUpgradeIssuerVersion = async (id: string, versionId: number): Promise<IssuerDeployment> => {
    const response = await axiosInstance.patch(`/deployment/issuer/${id}/upgrade/${versionId}`);
    return response.data;
};
