import { GetOfferingResponse, PostOfferingResponse } from '@/types/offerings';
import { SchemaResponse } from '@/types/schemaManager';
import axios from 'axios';

interface IssuerOfferingSchema {
    id: string;
    fullHost: string;
    authorization: string;
    payload: any;
}
export const handlePostIssuerOffering = async ({
    fullHost,
    authorization,
    payload,
}: Omit<IssuerOfferingSchema, 'id'>): Promise<PostOfferingResponse> => {
    const response = await axios.post(`${fullHost}/api/v1/offerings`, payload, {
        headers: {
            'Content-Type': 'application/json',
            'x-client-secret': authorization,
        },
    });
    return response.data;
};

export const handleGetIssuerOfferings = async ({
    fullHost,
    authorization,
}: Pick<IssuerOfferingSchema, 'fullHost' | 'authorization'>): Promise<Array<GetOfferingResponse>> => {
    const response = await axios.get(`${fullHost}/api/v1/offerings`, {
        headers: {
            'Content-Type': 'application/json',
            'x-client-secret': authorization,
        },
    });
    return response.data;
};

export const handleGetIssuerOfferingQrCodeById = async ({
    id,
    fullHost,
    authorization,
}: Omit<IssuerOfferingSchema, 'payload'>): Promise<string> => {
    const response = await axios.get(`${fullHost}/api/v1/offerings/${id}/qr-code`, {
        headers: {
            'Content-Type': 'application/json',
            'x-client-secret': authorization,
        },
    });
    return response.data;
};
