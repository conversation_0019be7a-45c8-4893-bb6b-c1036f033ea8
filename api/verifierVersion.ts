import axiosInstance from '@/middleware/axiosInstance';
import { VerifierDeployment } from '@/types/deploymentsVerifier';
import { VersionInfo } from '@/types/version';

export const handleGetAvailableVerifierVersions = async (): Promise<Array<VersionInfo>> => {
    const response = await axiosInstance.get(`/verifier-version`);
    return response.data;
};

export const handleUpgradeVerifierVersion = async (id: string, versionId: number): Promise<VerifierDeployment> => {
    const response = await axiosInstance.patch(`/deployment/verifier/${id}/upgrade/${versionId}`);
    return response.data;
};
