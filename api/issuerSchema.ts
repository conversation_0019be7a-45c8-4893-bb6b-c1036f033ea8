import { SchemaResponse } from '@/types/schemaManager';
import axios from 'axios';

interface PostIssuerSchema {
    fullHost: string;
    authorization: string;
    payload: any;
}
export const handlePostIssuerSchema = async ({
    fullHost,
    authorization,
    payload,
}: PostIssuerSchema): Promise<SchemaResponse> => {
    const response = await axios.post(`${fullHost}/api/v1/schemas`, payload, {
        headers: {
            'Content-Type': 'application/json',
            'x-client-secret': authorization,
        },
    });
    return response.data;
};

export const handlePostIssuerSchemaDraft = async ({
    fullHost,
    authorization,
    payload,
}: PostIssuerSchema): Promise<SchemaResponse> => {
    const response = await axios.post(`${fullHost}/api/v1/schemas/drafts`, payload, {
        headers: {
            'Content-Type': 'application/json',
            'x-client-secret': authorization,
        },
    });
    return response.data;
};

export const handleGetIssuerSchemas = async ({
    fullHost,
    authorization,
}: Pick<PostIssuerSchema, 'fullHost' | 'authorization'>): Promise<Array<SchemaResponse>> => {
    const response = await axios.get(`${fullHost}/api/v1/schemas`, {
        headers: {
            'Content-Type': 'application/json',
            'x-client-secret': authorization,
        },
    });
    return response.data;
};

export const handleGetIssuerSchemasDraft = async ({
    fullHost,
    authorization,
}: Pick<PostIssuerSchema, 'fullHost' | 'authorization'>): Promise<Array<SchemaResponse>> => {
    const response = await axios.get(`${fullHost}/api/v1/schemas/drafts`, {
        headers: {
            'Content-Type': 'application/json',
            'x-client-secret': authorization,
        },
    });
    return response.data;
};
