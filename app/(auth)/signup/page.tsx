'use client';

import { ButtonGradient } from '@/components/Buttons';
import { ItemBox } from '@/components/ItemBox';
import Link from 'next/link';
import { useSignupForm } from '@/hooks/useSignupForm';
import { useTranslations } from 'next-intl';
import { RouterPaths } from '@/common/routerPaths';
import { renderFormFields } from '@/common/renderFormFields';
import { SignupFormInputsTypes } from '@/validation/signupFormValidation';

const LOCALE_PREFIX = 'signup_screen';

const Signup = () => {
    const t = useTranslations(LOCALE_PREFIX);
    const { register, handleSubmit, errors, handleFormSubmit, trigger, isValid, formFields, isSubmitting, control } =
        useSignupForm();

    const fieldsToRender = renderFormFields({
        formFields,
        register,
        errors,
        t,
        checkboxHref: {
            termsAndConditions: RouterPaths.TERMS_AND_CONDITIONS,
            policy: RouterPaths.POLICY,
        },
        formInputsTypes: SignupFormInputsTypes,
        trigger,
        control,
    });

    return (
        <ItemBox>
            <div className="flex flex-col gap-4 w-box-100 px-box-100 py-box-50">
                <p className="text-2xl text-white w-full text-start">{t('title')}</p>
                <form onSubmit={handleSubmit(handleFormSubmit)} className="flex flex-col gap-2">
                    {fieldsToRender.slice(0, 4)}
                    <div className="flex flex-row gap-2">{fieldsToRender.slice(4, 6)}</div>
                    {fieldsToRender.slice(6, 7)}
                    <div>{fieldsToRender.slice(7, 10)}</div>
                    <ButtonGradient id="auth_signup_Page_button_c5uamh" type="submit" disabled={!isValid} isLoading={isSubmitting}>
                        {t('button')}
                    </ButtonGradient>
                    <div className="flex flex-row gap-1 pt-6 text-sm whitespace-nowrap">
                        <p className="text-white">{t('account_question')}</p>
                        <Link id="auth_signup_Page_link_nc217j" className="text-main-100" href={RouterPaths.LOGIN}>
                            {t('account_question_link')}
                        </Link>
                    </div>
                </form>
            </div>
        </ItemBox>
    );
};

export default Signup;
