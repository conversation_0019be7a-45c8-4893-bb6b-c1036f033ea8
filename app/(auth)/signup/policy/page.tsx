import { ItemBox } from '@/components/ItemBox';
import { markdownContent } from '@/data/policy';
import React from 'react';
import ReactMarkdown from 'react-markdown';

const Policy = () => {
    return (
        <div className="w-full py-4 h-full max-w-screen-md mx-auto overflow-hidden max-h-full">
            <div className="flex h-full px-4">
                <ItemBox>
                    <div className="overflow-scroll without-scrollbar flex-1 h-full w-full">
                        <ReactMarkdown className="markdown without-scrollbar flex flex-col overflow-scroll justify-center gap-2 p-8">
                            {markdownContent}
                        </ReactMarkdown>
                    </div>
                </ItemBox>
            </div>
        </div>
    );
};

export default Policy;
