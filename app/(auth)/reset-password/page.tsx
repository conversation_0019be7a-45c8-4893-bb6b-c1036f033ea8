'use client';

import { useTranslations } from 'next-intl';
import { useSearchParams } from 'next/navigation';
import { ButtonGradient } from '@/components/Buttons';
import { renderFormFields } from '@/common/renderFormFields';
import { usePasswordReset } from '@/hooks/usePasswordResetForm';
import { PasswordResetFormInputsTypes } from '@/validation/passwordResetValidation';
import { ItemBox } from '@/components/ItemBox';

const ResetPassword = () => {
    const t = useTranslations('reset_password');
    const searchParams = useSearchParams();

    const uuid = searchParams.get('uuid');
    const token = searchParams.get('token');

    if (!uuid || !token) {
        throw new Error('UUID and token are required for password reset');
    }

    const { formFields, register, handleSubmit, errors, control, handleFormSubmit } = usePasswordReset({
        uuid,
        token,
    });

    const fieldsToRender = renderFormFields({
        formFields,
        register,
        errors,
        t,
        formInputsTypes: PasswordResetFormInputsTypes,
        control,
    });

    return (
        <ItemBox>
            <div className="flex flex-col w-box-100 h-box-100 justify-evenly px-box-100 py-box-50">
                <h1 className="text-2xl font-bold text-left">{t('title')}</h1>
                <form>{fieldsToRender}</form>
                <ButtonGradient id="auth_reset-password_Page_button_fu8zcy" onClick={handleSubmit(handleFormSubmit)}>{t('button')}</ButtonGradient>
            </div>
        </ItemBox>
    );
};

export default ResetPassword;
