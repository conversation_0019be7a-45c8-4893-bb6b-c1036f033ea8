import { AnimLogo } from '@/components/AnimLogo/AnimLogo';

type SignupLayoutProps = {
    children: React.ReactNode;
};

export default async function SignupLayout({ children }: SignupLayoutProps) {
    return (
        <div className="flex flex-row items-center justify-between h-screen">
            <div className="flex-1 h-full flex justify-center items-center">
                <AnimLogo />
            </div>
            <div className="flex-1 h-full flex justify-center items-center">{children}</div>
        </div>
    );
}
