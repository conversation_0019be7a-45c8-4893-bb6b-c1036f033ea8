"use client";

import {useTranslations} from 'next-intl';
import {useEffect, useState} from 'react';
import {ItemBox} from '@/components/ItemBox';
import {LoaderSpinner} from '@/components/Loaders';
import {handleChangePassword} from "@/api/auth";

const Page = () => {
    const t = useTranslations('change_password');
    const [loading, setLoading] = useState(true);
    const [success, setSuccess] = useState(false);

    useEffect(() => {
        const changePassword = async () => {
            try {
                await handleChangePassword();
                setSuccess(true);
            } finally {
                setLoading(false);
            }
        };

        changePassword();
    }, [t]);

    if (loading) {
        return (
            <div className="flex items-center justify-center w-full h-full">
                <LoaderSpinner/>
            </div>
        );
    }

    if (success) {
        return (
            <ItemBox>
                <div className="w-box-100 h-box-100 px-box-100 py-box-100 flex flex-col justify-between">
                    <p>{t('label')}</p>
                    <div className="flex flex-col gap-3 flex-1 justify-center">
                        <h1 className="text-2xl font-bold">{t('success_title')}</h1>
                        <p>{t('success_description')}</p>
                    </div>
                </div>
            </ItemBox>
        );
    }

    return (
        <ItemBox>
            <div className="w-box-100 h-box-100 px-box-100 py-box-100 flex flex-col justify-center items-center gap-4">
                <h1 className="text-2xl font-bold">{t('error_title')}</h1>
            </div>
        </ItemBox>
    );
};

export default Page;
