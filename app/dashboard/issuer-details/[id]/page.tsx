'use client';

import { DeploymentType } from '@/types/deployments';
import { useDeleteDeployment } from '@/hooks/useDeleteDeployment';
import { useDeploymentDetails } from '@/hooks/useDeploymentDetails';
import { useUpgradeSecret } from '@/hooks/useUpgradeSecret';
import { useUpgradeIssuerVersionModal } from '@/hooks/useUpgradeIssuerVersion';
import { WidgetDetailsOfDeploymentIssuer } from '@/components/WidgetDetailsOfDeployment/WidgetDetailsOfDeploymentIssuer';

const TYPE = DeploymentType.ISSUER;

const DetailsOfIssuerPage = () => {
    const { handleConfirmDelete } = useDeleteDeployment({ type: TYPE });
    const { data } = useDeploymentDetails({ type: TYPE });
    const { handleConfirmUpgradeSecret } = useUpgradeSecret({
        type: TYPE,
    });
    const { handleShowLogicUpgradeVersionModal } = useUpgradeIssuerVersionModal();

    if (!data) return null;
    if (TYPE === DeploymentType.ISSUER) {
        return (
            <WidgetDetailsOfDeploymentIssuer
                handleDelete={() => handleConfirmDelete(data.id, data.issuerName || '')}
                handleUpgradeSecret={() => handleConfirmUpgradeSecret(data.id)}
                handleUpgradeVersion={() => handleShowLogicUpgradeVersionModal(data.id, data.version)}
                status={data.status}
                type={TYPE}
                deployment={data}
            />
        );
    }
    return null;
};

export default DetailsOfIssuerPage;
