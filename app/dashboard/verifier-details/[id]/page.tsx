'use client';

import { DeploymentType } from '@/types/deployments';
import { useDeleteDeployment } from '@/hooks/useDeleteDeployment';
import { useDeploymentDetails } from '@/hooks/useDeploymentDetails';
import { useUpgradeSecret } from '@/hooks/useUpgradeSecret';
import { useUpgradeVerifierVersionModal } from '@/hooks/useUpgradeVerifierVersion';
import { WidgetDetailsOfDeploymentVerifier } from '@/components/WidgetDetailsOfDeployment/WidgetDetailsOfDeploymentVerifier';

const TYPE = DeploymentType.VERIFIER;

const DetailsOfVerifierPage = () => {
    const { handleConfirmDelete } = useDeleteDeployment({
        type: TYPE,
    });
    const { data } = useDeploymentDetails({ type: TYPE });
    const { handleConfirmUpgradeSecret } = useUpgradeSecret({
        type: TYPE,
    });
    const { handleShowLogicUpgradeVersionModal } = useUpgradeVerifierVersionModal();

    if (!data) return null;
    if (TYPE === DeploymentType.VERIFIER) {
        return (
            <WidgetDetailsOfDeploymentVerifier
                handleDelete={() => handleConfirmDelete(data.id, data.verifierName || '')}
                handleUpgradeSecret={() => handleConfirmUpgradeSecret(data.id)}
                handleUpgradeVersion={() => handleShowLogicUpgradeVersionModal(data.id, data.version)}
                status={data.status}
                type={TYPE}
                deployment={data}
            />
        );
    }
    return null;
};

export default DetailsOfVerifierPage;
