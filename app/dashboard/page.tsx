'use client';
import { ItemBox } from '@/components/ItemBox';
import { LoaderSpinnerSmall } from '@/components/Loaders';
import { WidgetDeploymentsTable } from '@/components/WidgetDeploymentsTable/WidgetDeploymentsTable';
import { useDashboardLogic } from '@/hooks/useDashboardLogic';

const Dashboard = () => {
    const { deployments, isLoading, subscriptionActive } = useDashboardLogic();

    if (!subscriptionActive)
        return (
            <div className="flex justify-center items-center h-full py-32">
                <LoaderSpinnerSmall />
            </div>
        );

    return (
        <div className="relative overflow-hidden w-full h-full">
            <ItemBox>
                <WidgetDeploymentsTable isLoading={isLoading} deployments={deployments} />
            </ItemBox>
        </div>
    );
};

export default Dashboard;
