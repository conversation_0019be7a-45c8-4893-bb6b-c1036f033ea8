'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { handleGetUser } from '@/api/user';
import { PageSizeLoader } from '@/components/Loaders';
import { RouterPaths } from '@/common/routerPaths';

export default function SubscriptionAwaitPage() {
    const router = useRouter();

    useEffect(() => {
        const checkSubscription = async () => {
            const user = await handleGetUser();
            if (user.subscriptionActive) {
                clearInterval(intervalId);
                router.push(RouterPaths.DASHBOARD);
            }
        };

        checkSubscription();

        const intervalId = setInterval(checkSubscription, 1000);

        return () => {
            clearInterval(intervalId);
        };
    }, [router]);

    return <PageSizeLoader />;
}
