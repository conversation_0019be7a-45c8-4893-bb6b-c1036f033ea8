{
  "$metadata": {
    "version": "1.0",
    "type": "TestType"
  },
  "title": "Test",
  "$schema": "https://json-schema.org/draft/2020-12/schema",
  "properties": {
    "credentialSubject": {
      "description": "Stores the data of the test",
      "title": "Test subject",
      "properties": {
        "attribute_1": {
          "type": "string",
          "title": "Attribute 1",
          "description": "Stores the attribute 1 of the credential subject"
        },
        "attribute_2": {
          "type": "object",
          "title": "Attribute 2",
          "description": "Stores the attribute 2 of the credential subject",
          "properties": {
            "nested_attribute_1": {
              "type": "string",
              "title": "Nested Attribute 1",
              "description": "Stores the nested attribute 1 of the credential subject"
            },
            "nested_attribute_2": {
              "type": "string",
              "title": "Nested Attribute 2",
              "description": "Stores the nested attribute 2 of the credential subject"
            }
          }
        },
        "attribute_3": {
          "type": "uri",
          "title": "Attribute 3",
          "description": "Stores the attribute 3 of the credential subject"
        }
      },
      "required": [],
      "type": "object"
    },
    "@context": {
      "type": [
        "string",
        "array",
        "object"
      ]
    },
    "id": {
      "type": "string"
    },
    "issuanceDate": {
      "format": "date-time",
      "type": "string"
    },
    "issuer": {
      "type": [
        "string",
        "object"
      ],
      "format": "uri",
      "properties": {
        "id": {
          "format": "uri",
          "type": "string"
        }
      },
      "required": [
        "id"
      ]
    },
    "type": {
      "type": [
        "string",
        "array"
      ],
      "items": {
        "type": "string"
      }
    },
    "credentialSchema": {
      "properties": {
        "id": {
          "format": "uri",
          "type": "string"
        },
        "type": {
          "type": "string"
        }
      },
      "required": [
        "id",
        "type"
      ],
      "type": "object"
    }
  },
  "required": [
    "credentialSubject",
    "@context",
    "id",
    "issuanceDate",
    "issuer",
    "type",
    "credentialSchema"
  ],
  "type": "object"
}





{
    "version": 1.0,
    "type": "TestType",
    "name": "Test",
    "credentialSubject": {
        "description": "Stores the data of the test",
        "title": "Test subject",
        "properties": {
            "attribute_1": {
                "type": "string",
                "title": "Attribute 1",
                "description": "Stores the attribute 1 of the credential subject"
            },
            "attribute_2": {
                "type": "object",
                "title": "Attribute 2",
                "description": "Stores the attribute 2 of the credential subject",
                "properties": {
                    "nested_attribute_1": {
                        "type": "string",
                        "title": "Nested Attribute 1",
                        "description": "Stores the nested attribute 1 of the credential subject"
                    },
                    "nested_attribute_2": {
                        "type": "string",
                        "title": "Nested Attribute 2",
                        "description": "Stores the nested attribute 2 of the credential subject"
                    }
                }
            },
            "attribute_3": {
                "type": "uri",
                "title": "Attribute 3",
                "description": "Stores the attribute 3 of the credential subject"
            }
        },
        "required": [],
        "type": "object"
    }
}