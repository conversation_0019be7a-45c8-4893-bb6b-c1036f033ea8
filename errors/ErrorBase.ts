import { ErrorTypes } from '@/common/errorTypes';
import { getErrorMessage, getErrorStatus } from '@/common/getErrorStateInfo';
import { ErrorClassFields } from '@/types/errorClass';

export abstract class ErrorBase implements ErrorClassFields {
    type: ErrorTypes;
    code: number;
    message?: string;

    constructor(type: ErrorTypes, error?: unknown) {
        this.code = getErrorStatus(error);
        this.message = getErrorMessage(error);
        this.type = type;
    }
}
