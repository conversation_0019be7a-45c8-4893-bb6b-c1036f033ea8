import {
    ModalConfirmUpgradeVersion,
    ModalLogicIssuerUpgradeVersion,
    ModalSuccessUpgradeVersion,
} from '@/components/Modals';
import { useModal } from './useModal';
import { useErrorHandling } from './useErrorHandling';
import {
    createUpgradeIssuerVersionSchema,
    UpgradeIssuerVersionForm,
} from '@/validation/upgradeIssuerVersionValidation';
import { zodResolver } from '@hookform/resolvers/zod';
import { useTranslations } from 'next-intl';
import { useForm } from 'react-hook-form';
import { handleUpgradeIssuerVersion } from '@/api/issuerVersion';
import { useDeploymentDetailsStore } from '@/store/deploymentDetailsStore';
import { fetchDeploymentDetailsMethods } from '@/common/fetchMethods';
import { DeploymentType } from '@/types/deployments';
import { ErrorUpgradeDeploymentVersion } from '@/errors/ErrorUpgradeDeploymentVersion';

interface Props {
    deploymentId: string;
}

export const useUpgradeIssuerVersionModal = () => {
    const { showModal } = useModal();

    const handleShowLogicUpgradeVersionModal = (id: string, deploymentCurrentVersion: string) => {
        showModal(
            <ModalLogicIssuerUpgradeVersion deploymentId={id} deploymentCurrentVersion={deploymentCurrentVersion} />
        );
    };

    return {
        handleShowLogicUpgradeVersionModal,
    };
};

export const useUpgradeIssuerVersion = ({ deploymentId }: Props) => {
    const { showModal } = useModal();
    const { withErrorHandling } = useErrorHandling();
    const { setDeploymentDetails } = useDeploymentDetailsStore();
    const t = useTranslations();
    const schema = createUpgradeIssuerVersionSchema(t);
    const {
        register,
        handleSubmit,
        trigger,
        control,
        formState: { errors, isValid, isSubmitting },
    } = useForm<UpgradeIssuerVersionForm>({
        resolver: zodResolver(schema),
    });
    const formFields = schema.shape;

    const handleConfirmUpgradeVersion = (data: UpgradeIssuerVersionForm) => {
        showModal(<ModalConfirmUpgradeVersion onConfirm={() => handleUpgradeVersion(data)} />);
    };

    const handleUpgradeVersion = (data: UpgradeIssuerVersionForm) => {
        withErrorHandling(async () => {
            try {
                await handleUpgradeIssuerVersion(deploymentId, Number(data.version));
                const refetchDetails = await fetchDeploymentDetailsMethods[DeploymentType.ISSUER](
                    deploymentId.toString()
                );
                setDeploymentDetails(refetchDetails);
                showModal(<ModalSuccessUpgradeVersion />);
            } catch (error) {
                throw new ErrorUpgradeDeploymentVersion(error);
            }
        });
    };

    const onSubmit = (data: UpgradeIssuerVersionForm) => {
        handleConfirmUpgradeVersion(data);
    };

    return {
        trigger,
        formFields,
        register,
        handleSubmit,
        errors,
        isValid,
        isSubmitting,
        control,
        onSubmit,
    };
};
