import { SuccessToast } from '@/components/Toasts';
import { useToast } from './useToast';
import { useTranslations } from 'next-intl';

export const useCopyAndDownload = () => {
    const { showToast } = useToast();
    const t = useTranslations();

    const handleCopyToClipboard = async (content: string | string[]): Promise<boolean> => {
        try {
            // Convert array to string if needed
            const textToCopy = Array.isArray(content) ? content.join('\n') : content;

            await navigator.clipboard.writeText(textToCopy);
            showToast(<SuccessToast title={t('toast.success')} message={t('toast.copied_to_clipboard')} />);
            return true;
        } catch (error) {
            console.error('Failed to copy to clipboard:', error);
            return false;
        }
    };

    const handleDownloadFile = async (content: string | string[], filename: string): Promise<boolean> => {
        try {
            // Convert array to string if needed
            const textToDownload = Array.isArray(content) ? content.join('\n') : content;

            // Ensure filename has .txt extension
            const filenameWithExt = filename.endsWith('.txt') ? filename : `${filename}.txt`;

            // Create blob with text content
            const blob = new Blob([textToDownload], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);

            // Create temporary download link
            const link = document.createElement('a');
            link.href = url;
            link.download = filenameWithExt;

            // Trigger download
            document.body.appendChild(link);
            link.click();

            // Cleanup
            document.body.removeChild(link);
            URL.revokeObjectURL(url);

            return true;
        } catch (error) {
            console.error('Failed to download file:', error);
            return false;
        }
    };

    return {
        handleCopyToClipboard,
        handleDownloadFile,
    };
};
function useTranslation(): { t: any } {
    throw new Error('Function not implemented.');
}
