import { useEffect } from 'react';
import { useErrorHandling } from './useErrorHandling';
import { ErrorGetAvailableVersions } from '@/errors/ErrorGetAvailableVersions';
import { handleGetBlockchainConfigs } from '@/api/blockchainConfigs';
import { useBlockchainConfigsStore } from '@/store/blockchainConfigsStore';
import { availableTransformedBlockchainConfigs } from '@/common/transformVersion';

export const useGetAvailableBlockchainConfigs = () => {
    const { withErrorHandling } = useErrorHandling();
    const { blockchainConfigs, setBlockchainConfigs } = useBlockchainConfigsStore();

    const handleGetAvailableBlockchainConfigs = () =>
        withErrorHandling(async () => {
            try {
                const availableConfigs = await handleGetBlockchainConfigs();
                const transformedConfigs = availableTransformedBlockchainConfigs(availableConfigs);

                setBlockchainConfigs(transformedConfigs);
            } catch {
                throw new ErrorGetAvailableVersions();
            }
        });

    useEffect(() => {
        handleGetAvailableBlockchainConfigs();
    }, []);

    return {
        blockchainConfigs,
    };
};
