import {
    ModalConfirmUpgradeVersion,
    ModalLogicVerifierUpgradeVersion,
    ModalSuccessUpgradeVersion,
} from '@/components/Modals';
import { useModal } from './useModal';
import { useErrorHandling } from './useErrorHandling';
import { useTranslations } from 'next-intl';
import {
    createUpgradeVerifierVersionSchema,
    UpgradeVerifierVersionForm,
} from '@/validation/upgradeVerifierVersionValidation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { handleUpgradeVerifierVersion } from '@/api/verifierVersion';
import { fetchDeploymentDetailsMethods } from '@/common/fetchMethods';
import { useDeploymentDetailsStore } from '@/store/deploymentDetailsStore';
import { DeploymentType } from '@/types/deployments';
import { ErrorUpgradeDeploymentVersion } from '@/errors/ErrorUpgradeDeploymentVersion';

interface Props {
    deploymentId: string;
}

export const useUpgradeVerifierVersionModal = () => {
    const { showModal } = useModal();

    const handleShowLogicUpgradeVersionModal = (id: string, deploymentCurrentVersion: string) => {
        showModal(
            <ModalLogicVerifierUpgradeVersion deploymentId={id} deploymentCurrentVersion={deploymentCurrentVersion} />
        );
    };

    return {
        handleShowLogicUpgradeVersionModal,
    };
};

export const useUpgradeVerifierVersion = ({ deploymentId }: Props) => {
    const { showModal } = useModal();
    const { withErrorHandling } = useErrorHandling();
    const { setDeploymentDetails } = useDeploymentDetailsStore();
    const t = useTranslations();
    const schema = createUpgradeVerifierVersionSchema(t);
    const {
        register,
        handleSubmit,
        trigger,
        control,
        formState: { errors, isValid, isSubmitting },
    } = useForm<UpgradeVerifierVersionForm>({
        resolver: zodResolver(schema),
    });
    const formFields = schema.shape;

    const handleUpgradeVersion = (data: UpgradeVerifierVersionForm) => {
        withErrorHandling(async () => {
            try {
                await handleUpgradeVerifierVersion(deploymentId, Number(data.version));
                const refetchDetails = await fetchDeploymentDetailsMethods[DeploymentType.VERIFIER](
                    deploymentId.toString()
                );
                setDeploymentDetails(refetchDetails);
                showModal(<ModalSuccessUpgradeVersion />);
            } catch (error) {
                throw new ErrorUpgradeDeploymentVersion(error);
            }
        });
    };

    const handleConfirmUpgradeVersion = (data: UpgradeVerifierVersionForm) => {
        showModal(<ModalConfirmUpgradeVersion onConfirm={() => handleUpgradeVersion(data)} />);
    };

    const onSubmit = (data: UpgradeVerifierVersionForm) => {
        handleConfirmUpgradeVersion(data);
    };

    return {
        trigger,
        formFields,
        register,
        handleSubmit,
        errors,
        isValid,
        isSubmitting,
        control,
        onSubmit,
    };
};
