import { ModalContext } from '@/contexts/ModalContext';
import { ReactNode, useContext } from 'react';

export const useModal = () => {
    const { setModalContent, setFullSize } = useContext(ModalContext);

    const showModal = (content: ReactNode, fullSize?: boolean) => {
        setModalContent(content);
        setFullSize(fullSize || false);
    };

    const hideModal = () => {
        setModalContent(null);
    };

    return {
        showModal,
        hideModal,
    };
};
