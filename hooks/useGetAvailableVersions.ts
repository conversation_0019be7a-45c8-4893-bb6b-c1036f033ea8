import { handleGetAvailableIssuerVersions } from '@/api/issuerVersion';
import { handleGetAvailableVerifierVersions } from '@/api/verifierVersion';
import { availableSortedTransformedVersions } from '@/common/transformVersion';
import { useDeploymentVersionStore } from '@/store/deploymentVersionStore';
import { DeploymentType } from '@/types/deployments';
import { useEffect } from 'react';
import { useErrorHandling } from './useErrorHandling';
import { ErrorGetAvailableVersions } from '@/errors/ErrorGetAvailableVersions';

interface Props {
    type: DeploymentType;
    deploymentCurrentVersion?: string;
}

export const useGetAvailableVersions = ({ type, deploymentCurrentVersion }: Props) => {
    const { withErrorHandling } = useErrorHandling();
    const { issuerVersion, setIssuerVersion } = useDeploymentVersionStore();
    const { verifierVersion, setVerifierVersion } = useDeploymentVersionStore();

    const isIssuer = type === DeploymentType.ISSUER;
    const isVerifier = type === DeploymentType.VERIFIER;

    const handleGetAvailableTransformedIssuerVersions = () =>
        withErrorHandling(async () => {
            try {
                const availableVersions = await handleGetAvailableIssuerVersions();
                const transformedVersions = availableSortedTransformedVersions(
                    availableVersions,
                    deploymentCurrentVersion
                );
                setIssuerVersion(transformedVersions);
            } catch {
                throw new ErrorGetAvailableVersions();
            }
        });

    const handleGetAvailableTransformedVerifierVersions = () =>
        withErrorHandling(async () => {
            try {
                const availableVersions = await handleGetAvailableVerifierVersions();
                const transformedVersions = availableSortedTransformedVersions(
                    availableVersions,
                    deploymentCurrentVersion
                );
                setVerifierVersion(transformedVersions);
            } catch {
                throw new ErrorGetAvailableVersions();
            }
        });

    const handleGetAvailableVersions = async () => {
        if (isIssuer) {
            handleGetAvailableTransformedIssuerVersions();
        } else if (isVerifier) {
            handleGetAvailableTransformedVerifierVersions();
        }
    };

    useEffect(() => {
        handleGetAvailableVersions();
    }, [deploymentCurrentVersion, type]);

    if (isIssuer) {
        return {
            versions: issuerVersion,
        };
    } else if (isVerifier) {
        return {
            versions: verifierVersion,
        };
    } else {
        return {
            versions: [],
        };
    }
};
