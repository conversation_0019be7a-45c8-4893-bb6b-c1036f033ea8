import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { RouterPaths } from '@/common/routerPaths';
import { useErrorHandling } from './useErrorHandling';
import { handleResetPassword } from '@/api/auth';
import { createPasswordResetSchema, PasswordResetForm } from '@/validation/passwordResetValidation';

interface UsePasswordResetProps {
    uuid: string;
    token: string;
}

export const usePasswordReset = ({ uuid, token }: UsePasswordResetProps) => {
    const t = useTranslations();
    const schema = createPasswordResetSchema(t);
    const { withErrorHandling } = useErrorHandling();
    const router = useRouter();

    const {
        register,
        handleSubmit,
        control,
        setValue,
        formState: { errors, isValid, isSubmitting },
    } = useForm<PasswordResetForm>({
        resolver: zodResolver(schema),
        mode: 'onChange',
    });

    const formFields = schema.shape;

    const handleFormSubmit = (data: PasswordResetForm) =>
        withErrorHandling(async () => {
            await handleResetPassword(uuid, token, data.password);
            router.replace(RouterPaths.LOGIN);
        });

    return {
        formFields,
        register,
        handleSubmit,
        errors,
        handleFormSubmit,
        isValid,
        isSubmitting,
        control,
        setValue,
    };
};
