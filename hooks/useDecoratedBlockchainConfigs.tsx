import { useMemo } from 'react';
import { User } from '@/types/user';
import { SelectValue } from '@/types/selectValue';
import { Deployment, DeploymentType } from '@/types/deployments';
import { DecoratedBlockchainConfig } from '@/types/decoratedBlockchainConfig';

/**
 * Adds a "(X left)" suffix to every blockchain config.
 *
 * @param configs      Raw blockchain configs from the API.
 * @param user         Current user (must include metadata[]).
 * @param prefix       "VERIFIER", "ISSUER", …  —  matches the metadata key family.
 * @param deployments  All existing deployments of that prefix type (belonging to the user).
 */
export function useDecoratedBlockchainConfigsSelect(
    configs: SelectValue[] | undefined,
    user: User | null,
    prefix: DeploymentType,
    deployments: Deployment[] | undefined
): DecoratedBlockchainConfig[] {
    return useMemo(() => {
        if (!configs || !user) return [];

        const usedByChain: Record<string, number> = {};
        deployments?.forEach(dep => {
            const chain = dep.networkName;
            usedByChain[chain] = (usedByChain[chain] ?? 0) + 1;
        });

        return configs.map(cfg => {
            const chain = cfg.label.toUpperCase();
            const metaKey = `${prefix.toUpperCase()}_${chain}_DEPLOYMENT_LIMIT`;
            const limit = Number(user.metadata?.find(m => m.type === metaKey)?.value ?? 0);
            const left = Math.max(limit - (usedByChain[chain] ?? 0), 0);

            return {
                configs: { ...cfg, label: `${cfg.label} (${left} left)` },
                left,
            };
        });
    }, [configs, user, prefix, deployments]);
}
