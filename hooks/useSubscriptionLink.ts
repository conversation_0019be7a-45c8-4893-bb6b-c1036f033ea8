import { fetchPortalUrl } from '@/api/stripePortal';
import { RouterPaths } from '@/common/routerPaths';
import router from 'next/router';
import { useCallback } from 'react';

export const useSubscriptionLink = () => {
    const handlePortalClick = useCallback(async () => {
        try {
            const url = await fetchPortalUrl();
            if (!url) {
            } else {
                window.open(url, '_blank');
            }
        } catch {
            router.replace(RouterPaths.PAYMENT);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return { handlePortalClick };
};
