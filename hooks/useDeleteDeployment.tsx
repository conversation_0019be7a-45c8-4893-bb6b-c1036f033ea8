import { DeploymentType } from '@/types/deployments';
import { useRouter } from 'next/navigation';
import { useModal } from './useModal';
import { deleteDeploymentMethods } from '@/common/fetchMethods';
import { ModalConfirmDeleteDeployment, ModalSuccessDeleteDeployment } from '@/components/Modals';
import { useErrorHandling } from './useErrorHandling';
import { ErrorDeleteDeployment } from '@/errors/ErrorDeleteDeployment';

interface Props {
    type: DeploymentType;
}

export const useDeleteDeployment = ({ type }: Props) => {
    const { showModal, hideModal } = useModal();
    const { withErrorHandling } = useErrorHandling();
    const router = useRouter();

    const handleDelete = (id: string) =>
        withErrorHandling(async () => {
            try {
                await deleteDeploymentMethods[type](id.toString());
                showModal(<ModalSuccessDeleteDeployment />);
                router.push('/dashboard');
            } catch (error) {
                hideModal();
                throw new ErrorDeleteDeployment(error);
            }
        });

    const handleConfirmDelete = (id: string, name: string) => {
        showModal(<ModalConfirmDeleteDeployment onConfirm={() => handleDelete(id)} deploymentName={name} />);
    };

    return { handleConfirmDelete };
};
