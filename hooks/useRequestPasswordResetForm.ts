'use client';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useTranslations } from 'next-intl';
import {
    createRequestPasswordResetSchema,
    RequestPasswordResetFormData,
} from '@/validation/requestPasswordResetValidation';
import { useErrorHandling } from './useErrorHandling';
import { handleRequestPasswordReset } from '@/api/auth';

interface UseRequestPasswordResetFormProps {
    email: string | undefined;
    onSuccess: (data: RequestPasswordResetFormData) => void;
}

/**
 * Custom hook to manage the request password reset form logic.
 * @param onSuccess - Callback function to execute on successful form submission.
 */
export const useRequestPasswordResetForm = ({ onSuccess, email }: UseRequestPasswordResetFormProps) => {
    const t = useTranslations();
    const { withErrorHandling } = useErrorHandling();
    const schema = createRequestPasswordResetSchema(t);

    const isEmailDisabled = Boolean(email);

    const {
        register,
        handleSubmit,
        control,
        formState: { errors, isValid, isSubmitting },
    } = useForm<RequestPasswordResetFormData>({
        resolver: zodResolver(schema),
        defaultValues: {
            email,
        },
        mode: 'onBlur',
    });

    const formFields = schema.shape;

    const handleFormSubmit = (data: RequestPasswordResetFormData) =>
        withErrorHandling(async () => {
            await handleRequestPasswordReset(data.email);
            onSuccess(data);
        });

    return {
        formFields,
        register,
        handleSubmit,
        errors,
        handleFormSubmit,
        isValid,
        isSubmitting,
        control,
        isEmailDisabled,
    };
};
