import { useErrorHandling } from './useErrorHandling';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { fetchLogsMethods } from '@/common/fetchMethods';
import { DeploymentType } from '@/types/deployments';
import { ErrorUpgradeDeploymentVersion } from '@/errors/ErrorUpgradeDeploymentVersion';
import { useLogsStore } from '@/store/logsStore';
import { createGetLogsSchema, GetLogsForm } from '@/validation/logsSchemaValidation';
import { useEffect, useState } from 'react';

interface Props {
    type: DeploymentType;
    deploymentId: string;
}

export const useShowLogs = ({ type, deploymentId }: Props) => {
    const { withErrorHandling } = useErrorHandling();
    const [isLoading, setIsLoading] = useState(true);
    const { setLogs } = useLogsStore();
    const schema = createGetLogsSchema();

    const now = new Date();
    const endOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59);

    const oneMonthAgo = new Date(now);

    oneMonthAgo.setMonth(now.getMonth() - 1);
    const startOfDayOneMonthAgo = new Date(
        oneMonthAgo.getFullYear(),
        oneMonthAgo.getMonth(),
        oneMonthAgo.getDate(),
        0,
        0,
        0
    );

    const timezoneOffset = now.getTimezoneOffset() * 60000;

    const defaultStartTime = new Date(startOfDayOneMonthAgo.getTime() - timezoneOffset).toISOString().slice(0, 16);
    const defaultEndTime = new Date(endOfDay.getTime() - timezoneOffset).toISOString().slice(0, 16);

    const {
        register,
        handleSubmit,
        trigger,
        reset,
        control,
        formState: { errors, isValid, isSubmitting },
    } = useForm<GetLogsForm>({
        resolver: zodResolver(schema),
        defaultValues: {
            startTime: defaultStartTime,
            endTime: defaultEndTime,
        },
    });
    const formFields = schema.shape;

    const handleShowLogs = (data: GetLogsForm) => {
        withErrorHandling(async () => {
            try {
                setIsLoading(true);
                let isoStartTime;
                let isoEndTime;

                if (data.startTime) {
                    isoStartTime = new Date(data.startTime).toISOString();
                }

                if (data.endTime) {
                    isoEndTime = new Date(data.endTime).toISOString();
                }

                const refetchDetails = await fetchLogsMethods[type](
                    deploymentId,
                    isoStartTime,
                    isoEndTime,
                    data.search
                );
                setLogs(refetchDetails);
                setIsLoading(false);
            } catch (error) {
                throw new ErrorUpgradeDeploymentVersion(error);
            }
        });
    };

    const onSubmit = (data: GetLogsForm) => {
        handleShowLogs(data);
    };

    const onReset = () => {
        reset();
        handleShowLogs({});
    };

    useEffect(() => {
        handleShowLogs({});
    }, []);

    return {
        trigger,
        formFields,
        register,
        handleSubmit,
        onReset,
        errors,
        isValid,
        isSubmitting,
        control,
        onSubmit,
        isLoading,
    };
};
