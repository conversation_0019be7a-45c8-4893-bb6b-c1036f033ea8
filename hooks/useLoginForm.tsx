import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { createLoginSchema, LoginFormData } from '@/validation/loginFormValidation';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { RouterPaths } from '@/common/routerPaths';
import { useErrorHandling } from './useErrorHandling';
import { handleLoginIn } from '@/api/user';
import { useAuthStore } from '@/store/authStore';
import { ErrorUserLogin } from '@/errors/ErrorUserLogin';

export const useLoginForm = () => {
    const t = useTranslations();
    const { setAuthTokens } = useAuthStore();
    const schema = createLoginSchema(t);
    const { withErrorHandling } = useErrorHandling();
    const router = useRouter();
    const {
        register,
        handleSubmit,
        trigger,
        formState: { errors, isValid, isSubmitting },
    } = useForm<LoginFormData>({
        resolver: zodResolver(schema),
    });

    const formFields = schema.shape;

    const handleFormSubmit = (data: LoginFormData) =>
        withErrorHandling(async () => {
            try {
                const response = await handleLoginIn(data);
                setAuthTokens(response.token, response.refreshToken, response.zendeskToken);
                router.push(RouterPaths.DASHBOARD);
            } catch (error) {
                throw new ErrorUserLogin(error);
            }
        });

    return {
        formFields,
        register,
        handleSubmit,
        errors,
        handleFormSubmit,
        trigger,
        isValid,
        isSubmitting,
    };
};
