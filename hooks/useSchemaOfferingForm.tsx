import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useTranslations } from 'next-intl';
import { useMemo } from 'react';
import {
    createSchemaOfferingValidation,
    createSchemaOfferingFormTypes,
    createSelectOptionsFromSchema,
    SchemaOfferingFormData,
} from '@/validation/schemaOfferingValidation';
import { IssuerSchema } from '@/types/issuerSchema';
import { renderFormFields } from '@/common/renderFormFields';
import { useErrorHandling } from './useErrorHandling';
import { handlePostIssuerOffering } from '@/api/issuerOffering';
import { ErrorCreateOfferingIssuer } from '@/errors/ErrorCreateOfferingIssuer';
import { useDeploymentDetails } from './useDeploymentDetails';
import { DeploymentType } from '@/types/deployments';
import { useGetIssuerOfferings } from './useGetIssuerOfferings';

interface UseSchemaOfferingFormProps {
    schema: IssuerSchema;
}

export const useSchemaOfferingForm = ({ schema }: UseSchemaOfferingFormProps) => {
    const { data } = useDeploymentDetails({ type: DeploymentType.ISSUER });
    const t = useTranslations('schema_offering');
    const tValidation = useTranslations();
    const { withErrorHandling } = useErrorHandling();

    // Hook to invalidate offerings list after creating new offering
    const { invalidate: invalidateOfferings } = useGetIssuerOfferings({
        fullHost: data?.fullHost,
        authorization: data?.authKey || undefined,
    });

    // Extract credentialSubject schema
    const credentialSubjectSchema = useMemo(() => {
        if (!schema?.schemaBody?.properties?.credentialSubject) {
            throw new Error('Invalid schema: missing credentialSubject');
        }
        return schema.schemaBody.properties.credentialSubject;
    }, [schema]);

    // Create dynamic validation schema
    const validationSchema = useMemo(() => {
        return createSchemaOfferingValidation(credentialSubjectSchema, tValidation);
    }, [credentialSubjectSchema, tValidation]);

    // Create form field types mapping
    const formFieldTypes = useMemo(() => {
        return createSchemaOfferingFormTypes(credentialSubjectSchema);
    }, [credentialSubjectSchema]);

    // Create select options for enum fields
    const selectOptions = useMemo(() => {
        return createSelectOptionsFromSchema(credentialSubjectSchema);
    }, [credentialSubjectSchema]);

    // Initialize react-hook-form
    const {
        handleSubmit,
        formState: { errors, isValid, isSubmitting },
        register,
        control,
        trigger,
        reset,
        watch,
        setValue,
        getValues,
    } = useForm<SchemaOfferingFormData>({
        resolver: zodResolver(validationSchema),
        mode: 'onChange',
        reValidateMode: 'onChange',
    });

    // Create form fields object for renderFormFields
    const formFields = useMemo(() => {
        const fields: Record<string, any> = {};

        if (credentialSubjectSchema.properties) {
            Object.keys(credentialSubjectSchema.properties).forEach(fieldName => {
                fields[fieldName] = fieldName; // Simple mapping for renderFormFields
            });
        }

        return fields;
    }, [credentialSubjectSchema]);

    // Render form fields using existing renderFormFields utility
    const fieldsToRender = useMemo(() => {
        return renderFormFields({
            formFields,
            register,
            errors,
            t,
            formInputsTypes: formFieldTypes,
            trigger,
            control,
            selectOptions,
            skipTranslation: true,
        });
    }, [formFields, register, errors, t, formFieldTypes, trigger, control, selectOptions]);

    // Submit handler

    const onSubmit = (formData: SchemaOfferingFormData) =>
        withErrorHandling(async () => {
            try {
                if (!data) return;
                const { fullHost, authKey } = data;

                const response = await handlePostIssuerOffering({
                    fullHost: fullHost,
                    authorization: authKey || '',
                    payload: {
                        credential_subject: formData,
                        credential_type: schema.type,
                    },
                });

                console.log('Response:', response);

                // Invalidate offerings list to refresh it
                invalidateOfferings();
            } catch (error) {
                throw new ErrorCreateOfferingIssuer(error);
            }
        });

    // Get field labels from schema
    const getFieldLabel = (fieldName: string): string => {
        const fieldSchema =
            credentialSubjectSchema.properties?.[fieldName as keyof typeof credentialSubjectSchema.properties];
        return fieldSchema?.title || fieldName;
    };

    // Check if field is required
    const isFieldRequired = (fieldName: string): boolean => {
        return credentialSubjectSchema.required?.includes(fieldName) || false;
    };

    return {
        // Form methods
        onSubmit: handleSubmit(onSubmit),
        register,
        control,
        trigger,
        reset,
        watch,
        setValue,
        getValues,

        // Form state
        errors,
        isValid,
        isSubmitting,

        // Rendered fields
        fieldsToRender,

        // Schema info
        schema,
        credentialSubjectSchema,
        formFieldTypes,
        selectOptions,

        // Helper methods
        getFieldLabel,
        isFieldRequired,
    };
};
