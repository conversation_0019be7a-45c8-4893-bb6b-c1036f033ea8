'use client';
import { RefObject, useCallback, useEffect, useMemo, useState } from 'react';
import type { Placement } from '@/contexts/HintContext';

// Calculate best placement and coordinates based on viewport and element rects
export function useAutoPosition(
    anchorRef: RefObject<HTMLElement>,
    contentRef: RefObject<HTMLElement>,
    desired: Placement = 'auto',
    offset = 8
) {
    const [coords, setCoords] = useState<{ top: number; left: number }>({ top: -9999, left: -9999 });
    const [placement, setPlacement] = useState<Exclude<Placement, 'auto'>>('right');

    const compute = useCallback(() => {
        const anchor = anchorRef.current;
        const content = contentRef.current;
        if (!anchor || !content) return;

        const a = anchor.getBoundingClientRect();
        const c = content.getBoundingClientRect();
        const vw = window.innerWidth;
        const vh = window.innerHeight;

        const candidates: Exclude<Placement, 'auto'>[] =
            desired === 'auto' ? ['right', 'left', 'bottom', 'top'] : [desired];

        const fits = {
            right: a.right + offset + c.width <= vw && a.top + c.height / 2 <= vh && a.top - c.height / 2 >= 0,
            left: a.left - offset - c.width >= 0 && a.top + c.height / 2 <= vh && a.top - c.height / 2 >= 0,
            bottom: a.bottom + offset + c.height <= vh,
            top: a.top - offset - c.height >= 0,
        } as const;

        let chosen: Exclude<Placement, 'auto'> = candidates[0];
        for (const p of candidates) {
            if (fits[p]) {
                chosen = p;
                break;
            }
        }

        // Calculate position relative to viewport, then convert to absolute page coords via scroll
        let top = 0;
        let left = 0;

        switch (chosen) {
            case 'right':
                top = a.top + a.height / 2 - c.height / 2;
                left = a.right + offset;
                break;
            case 'left':
                top = a.top + a.height / 2 - c.height / 2;
                left = a.left - c.width - offset;
                break;
            case 'bottom':
                top = a.bottom + offset;
                left = a.left + a.width / 2 - c.width / 2;
                break;
            case 'top':
                top = a.top - c.height - offset;
                left = a.left + a.width / 2 - c.width / 2;
                break;
        }

        // account for scroll position
        top += window.scrollY;
        left += window.scrollX;

        // clamp slightly to viewport to avoid overflow
        const margin = 8;
        left = Math.max(margin + window.scrollX, Math.min(left, window.scrollX + vw - c.width - margin));
        top = Math.max(margin + window.scrollY, Math.min(top, window.scrollY + vh - c.height - margin));

        setPlacement(chosen);
        setCoords({ top, left });
    }, [anchorRef, contentRef, desired, offset]);

    // Recompute on open, resize, scroll
    useEffect(() => {
        const onScroll = () => requestAnimationFrame(compute);
        const onResize = () => requestAnimationFrame(compute);
        window.addEventListener('scroll', onScroll, { passive: true });
        window.addEventListener('resize', onResize);
        compute();
        return () => {
            window.removeEventListener('scroll', onScroll);
            window.removeEventListener('resize', onResize);
        };
    }, [compute]);

    return useMemo(() => ({ coords, placement, recompute: compute }), [coords, placement, compute]);
}
