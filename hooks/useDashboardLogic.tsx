import { handleGetUser } from '@/api/user';
import { fetchDeploymentsMethods } from '@/common/fetchMethods';
import { DeploymentsTableTabsContext } from '@/contexts/DeploymentsTableTabsContext';
import { useDeploymentsStore } from '@/store/deploymentsStore';
import { useUserStore } from '@/store/userStore';
import { useContext, useEffect, useState, useRef } from 'react';
import { useErrorHandling } from './useErrorHandling';
import { ErrorGetUserDeployments } from '@/errors/ErrorGetUserDeployments';
import { useRouter } from 'next/navigation';
import { RouterPaths } from '@/common/routerPaths';
import { DeploymentType, DeploymentStatus } from '@/types/deployments';

export const useDashboardLogic = () => {
    const { deployments, setDeployments } = useDeploymentsStore();
    const { setUser, user } = useUserStore();
    const router = useRouter();
    const [isLoading, setIsLoading] = useState(true);
    const { selectedDeploymentType, setSelectedDeploymentType } = useContext(DeploymentsTableTabsContext);
    const { withErrorHandling } = useErrorHandling();
    const intervalRef = useRef<NodeJS.Timeout | null>(null);

    const finalStatuses = [DeploymentStatus.FAILED, DeploymentStatus.ACTIVE, DeploymentStatus.DELETED_SCHEDULED];

    const hasOnlyFinalStatuses = (deployments: any[]) => {
        return deployments.every(deployment => finalStatuses.includes(deployment.status));
    };

    const fetchDeployments = () =>
        withErrorHandling(async () => {
            try {
                const deploymentsIssuer = await fetchDeploymentsMethods[DeploymentType.ISSUER]();
                const deploymentsVerifier = await fetchDeploymentsMethods[DeploymentType.VERIFIER]();

                setDeployments([...deploymentsIssuer, ...deploymentsVerifier]);
            } catch (error) {
                throw new ErrorGetUserDeployments(error);
            }
        });

    const fetchAllData = () =>
        withErrorHandling(async () => {
            setIsLoading(true);
            try {
                const [deploymentsIssuer, deploymentsVerifier, user] = await Promise.all([
                    fetchDeploymentsMethods[DeploymentType.ISSUER](),
                    fetchDeploymentsMethods[DeploymentType.VERIFIER](),
                    handleGetUser(),
                ]);
                setUser(user);
                setDeployments([...deploymentsIssuer, ...deploymentsVerifier]);
                if (!user.subscriptionActive) {
                    router.push(RouterPaths.PAYMENT);
                }
            } catch (error) {
                throw new ErrorGetUserDeployments(error);
            } finally {
                setIsLoading(false);
            }
        });

    const setupInterval = () => {
        if (intervalRef.current) {
            clearInterval(intervalRef.current);
        }

        const interval = hasOnlyFinalStatuses(deployments) ? 60000 : 3000;

        intervalRef.current = setInterval(() => {
            fetchDeployments();
        }, interval);
    };

    useEffect(() => {
        fetchAllData();
    }, [selectedDeploymentType]);

    useEffect(() => {
        setupInterval();

        return () => {
            if (intervalRef.current) {
                clearInterval(intervalRef.current);
            }
        };
    }, [deployments]);

    return {
        deployments,
        subscriptionActive: user?.subscriptionActive,
        isLoading,
        selectedDeploymentType,
        setSelectedDeploymentType,
    };
};
