'use client';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { RouterPaths } from '@/common/routerPaths';
import { createSignupSchema, createSignupSchemaWithRefine, SignupFormData } from '@/validation/signupFormValidation';
import { useErrorHandling } from './useErrorHandling';
import { handleSignup } from '@/api/user';
import { ErrorUserSignup } from '@/errors/ErrorUserSignup';

export const useSignupForm = () => {
    const t = useTranslations();
    const { withErrorHandling } = useErrorHandling();
    const schemaWithRefine = createSignupSchemaWithRefine(t);
    const schema = createSignupSchema(t);
    const router = useRouter();

    const {
        register,
        handleSubmit,
        trigger,
        control,
        formState: { errors, isValid, isSubmitting },
    } = useForm<SignupFormData>({
        resolver: zodResolver(schemaWithRefine),
        mode: 'all',
        reValidateMode: 'onChange',
    });

    const formFields = schema.shape;

    const handleFormSubmit = (data: SignupFormData) =>
        withErrorHandling(async () => {
            try {
                const { repeatPassword, termsAndConditions, ...requestData } = data;

                const res = await handleSignup(requestData);
                router.push(`${RouterPaths.CONFIRM_EMAIL}?uuid=${res.uuid}`);
            } catch (error) {
                throw new ErrorUserSignup(error);
            }
        });

    return {
        formFields,
        register,
        handleSubmit,
        errors,
        handleFormSubmit,
        trigger,
        isValid,
        isSubmitting,
        control,
    };
};
