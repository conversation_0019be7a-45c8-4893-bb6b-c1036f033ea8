'use client';
import { useConfigStore } from '@/store/configStore';
import { useEffect } from 'react';
import { useErrorHandling } from './useErrorHandling';
import { ErrorGetEnvConfig } from '@/errors/ErrorGetEnvConfig';
import { EnvConfig } from '@/types/configEnv';

export const useSetupEnv = () => {
    const { setEnv } = useConfigStore();
    const { withErrorHandling } = useErrorHandling();

    useEffect(() => {
        const fetchData = () =>
            withErrorHandling(async () => {
                try {
                    const response = await fetch('/config/env.json');
                    if (!response.ok) {
                        throw new ErrorGetEnvConfig();
                    }
                    const data = await response.json();
                    setEnv(data as EnvConfig);
                } catch (error) {
                    throw new ErrorGetEnvConfig(error);
                }
            });

        fetchData();
    }, []);
};
