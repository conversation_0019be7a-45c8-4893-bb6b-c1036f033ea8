import {
    DefineSchemaFormData,
    DefineSchemaFormInputsTypes,
    defineSchemaValidation,
} from '@/validation/issuerAdminSchemaManager';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { useDeploymentDetails } from './useDeploymentDetails';
import { DeploymentType } from '@/types/deployments';
import { useTranslations } from 'next-intl';
import { renderFormFields } from '@/common/renderFormFields';
import { useEffect } from 'react';
import { useGetAndSetSchemasOfIssuer } from './useGetAndSetSchemasOfIssuer';
import { useErrorHandling } from './useErrorHandling';
import { handlePostIssuerSchemaDraft } from '@/api/issuerSchema';
import { ErrorCreateNewIssuerSchema } from '@/errors/ErrorCreateNewIssuerSchema';
import { SchemaBuilderStepEnum, useSchemaBuilderContext } from '@/contexts/SchemaBuilderContext';
import { convertSchemaBodyToAttributes } from '@/common/convertSchemaBodyToAttributes';
import { generateJsonSchema } from '@/common/schemaBuilderTools';

interface Props {
    setActiveDefineSchema: (active: boolean) => void;
}

export const useDefineSchema = ({ setActiveDefineSchema }: Props) => {
    const { data, id } = useDeploymentDetails({ type: DeploymentType.ISSUER });
    const { setStep, setActiveVersionId, activeVersionId } = useSchemaBuilderContext();
    const { withErrorHandling } = useErrorHandling();
    const { data: schemas = [], refetch } = useGetAndSetSchemasOfIssuer({
        fullHost: data?.fullHost,
        authorization: data?.authKey || undefined,
    });
    const t = useTranslations();
    const t_ = useTranslations('schema_builder');
    const schema = defineSchemaValidation(t);

    const {
        handleSubmit,
        formState: { isValid, errors },
        register,
        reset,
        control,
        trigger,
    } = useForm<DefineSchemaFormData>({
        resolver: zodResolver(schema),
        mode: 'onChange',
        reValidateMode: 'onChange',
    });

    const formFields = schema.shape;

    const onSubmit = (_form: DefineSchemaFormData) => {
        withErrorHandling(async () => {
            try {
                if (!data) return;
                const { fullHost, authKey } = data;

                const newSchema = await handlePostIssuerSchemaDraft({
                    fullHost: fullHost,
                    authorization: authKey || '',
                    payload: _form,
                });

                await refetch();

                setActiveVersionId(newSchema.id);
                setActiveDefineSchema(false);
                setStep(SchemaBuilderStepEnum.SCHEMA_BUILDER);
            } catch (error) {
                throw new ErrorCreateNewIssuerSchema(error);
            }
        });
    };

    const handleAddNewVersion = () => {
        withErrorHandling(async () => {
            try {
                if (!data) return;
                const { fullHost, authKey } = data;
                const schema = schemas.find(s => s.id === activeVersionId);

                if (schema) {
                    const setupData: any = {
                        name: schema.name,
                        version: schema.version,
                        type: schema.type,
                        description: schema.description,
                    };

                    if (schema.schemaBody) {
                        const attributes = convertSchemaBodyToAttributes(schema.schemaBody);
                        setupData['attributes'] = attributes;
                    }

                    const jsonSchema = generateJsonSchema(setupData);

                    // Transform the schema to the required format
                    const transformedPayload = {
                        type: jsonSchema.$metadata.type,
                        name: jsonSchema.title,
                        description: jsonSchema.description,
                        credentialSubject: jsonSchema.properties.credentialSubject,
                    };

                    const newSchema = await handlePostIssuerSchemaDraft({
                        fullHost: fullHost,
                        authorization: authKey || '',
                        payload: transformedPayload,
                    });

                    await refetch();

                    setActiveVersionId(newSchema.id);
                    setActiveDefineSchema(false);
                    setStep(SchemaBuilderStepEnum.SCHEMA_BUILDER);
                } else {
                    throw new Error('Schema not found');
                }
            } catch (error) {
                throw new ErrorCreateNewIssuerSchema(error);
            }
        });
    };

    const handleContinueEditing = () => {
        setActiveDefineSchema(false);
        setStep(SchemaBuilderStepEnum.SCHEMA_BUILDER);
    };

    const handleSchemaOffering = (lastPublishedVersionId: string) => {
        setActiveDefineSchema(false);
        setActiveVersionId(lastPublishedVersionId);
        setStep(SchemaBuilderStepEnum.SCHEMA_OFFERING);
    };

    const fieldsToRender = renderFormFields({
        formFields,
        register,
        errors,
        t: t_,
        formInputsTypes: DefineSchemaFormInputsTypes,
        trigger,
        control,
    });

    useEffect(() => {
        if (data?.id && data.fullHost && data.authKey) {
            refetch();
        }
    }, [data, refetch]);

    return {
        fieldsToRender,
        issuerId: id,
        schemas,
        reset,
        onSubmit,
        isValid,
        handleSubmit,
        handleAddNewVersion,
        handleContinueEditing,
        setActiveVersionId,
        handleSchemaOffering,
        activeVersionId,
    };
};
