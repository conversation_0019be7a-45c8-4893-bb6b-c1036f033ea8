import { ToastContext } from '@/contexts/ToastContext';
import { ReactNode, useContext } from 'react';

export const useToast = () => {
    const { setToastContent } = useContext(ToastContext);

    const showToast = (content: ReactNode) => {
        setToastContent(content);
    };

    const hideToast = () => {
        setToastContent(null);
    };

    return {
        showToast,
        hideToast,
    };
};
