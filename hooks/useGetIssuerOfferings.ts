import { useQuery, useQueryClient } from '@tanstack/react-query';
import { handleGetIssuerOfferings } from '@/api/issuerOffering';
import { GetOfferingResponse } from '@/types/offerings';

const queryKey = (fullHost?: string, authorization?: string) => ['issuer-offerings', fullHost ?? '', authorization ?? ''];

export const useGetIssuerOfferings = ({
    fullHost,
    authorization,
}: {
    fullHost?: string;
    authorization?: string;
} = {}) => {
    const enabled = Boolean(fullHost && authorization);
    const qc = useQueryClient();

    const query = useQuery<GetOfferingResponse[]>({
        queryKey: queryKey(fullHost, authorization),
        queryFn: () => handleGetIssuerOfferings({ fullHost: fullHost!, authorization: authorization! }),
        enabled,
    });

    const refresh = async () => {
        if (!enabled) return;
        await qc.invalidateQueries({ queryKey: queryKey(fullHost, authorization) });
        await query.refetch();
    };

    const invalidate = () => {
        if (!enabled) return;
        qc.invalidateQueries({ queryKey: queryKey(fullHost, authorization) });
    };

    return { ...query, refresh, invalidate };
};
