'use server';

import { Cookies } from '@/common/cookies';
import { SUPPORTED_LOCALES } from '@/common/supportedLocales';
import { cookies } from 'next/headers';

const DEFAULT_LOCALE = SUPPORTED_LOCALES.en;

export async function getLocale() {
    const cookieStore = await cookies();

    return cookieStore.get(Cookies.LOCALE)?.value || DEFAULT_LOCALE;
}

export async function setLocale(locale: string | undefined) {
    try {
        const cookieStore = await cookies();
        const finalLocale =
            !locale || !(locale in SUPPORTED_LOCALES) ? DEFAULT_LOCALE : (locale as keyof typeof SUPPORTED_LOCALES);

        cookieStore.set(Cookies.LOCALE, finalLocale);
        return true;
    } catch (error) {
        console.error('Failed to set locale:', error);
        return false;
    }
}
